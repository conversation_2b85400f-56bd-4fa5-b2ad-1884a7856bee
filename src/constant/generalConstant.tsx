import { FilterParams, PaginationParams } from '@/api/core/types';

export const pageSizeDropdown = [
  { label: 20, value: 20 },
  { label: 50, value: 50 },
  { label: 100, value: 100 },
];

export const defaultPagination: PaginationParams & FilterParams = {
  pageNumber: 1,
  pageSize: 100,
};

export const MEME_TYPES = {
  pdf: 'application/pdf',
  csv: 'text/csv',
  excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  excel2: 'application/vnd.ms-excel',
  image: 'image/*',
};
