export const filterableModules = {
  address: {
    searchTerms: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
    ],
    advanceFilter: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
    ],
    sortable: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
      'createdAt',
    ],
  },
  contact: {
    searchTerms: ['name', 'email', 'phoneNumber'],
    advanceFilter: [
      'name',
      'email',
      'phoneNumber',
      'phoneCountryCode',
      'isPrimary',
      'emailVerified',
      'userId',
      'createdAt',
      'updatedAt',
    ],
    sortable: [
      'name',
      'email',
      'phoneNumber',
      'phoneCountryCode',
      'isPrimary',
      'emailVerified',
      'createdAt',
      'updatedAt',
      'isDeleted',
    ],
  },
  order: {
    advanceFilter: [
      'trackingNumber',
      'referenceNumber',
      'status',
      'customerId',
      'scheduledCollectionTime',
      'scheduledDeliveryTime',
      'billingStatus',
      'paymentStatus',
      'collectionAddressId',
      'deliveryAddressId',
      'totalPrice',
      'createdAt',
      'updatedAt',
    ],
    searchTerms: ['trackingNumber', 'referenceNumber', 'description', 'comments', 'internalNotes'],
    sortable: [
      'trackingNumber',
      'referenceNumber',
      'status',
      'scheduledCollectionTime',
      'scheduledDeliveryTime',
      'totalPrice',
      'billingStatus',
      'paymentStatus',
      'createdAt',
      'updatedAt',
    ],
  },
  invoice: {
    advanceFilter: [
      'invoiceNumber',
      'status',
      'customerId',
      'subtotal',
      'totalAmount',
      'pendingAmount',
      'refundAmount',
      'invoiceDate',
      'dueDate',
      // 'aging',
    ],
    searchTerms: ['invoiceNumber', 'status', 'notes'],
    sortable: [
      'invoiceNumber',
      'status',
      'subtotal',
      'totalAmount',
      'pendingAmount',
      'refundAmount',
      'invoiceDate',
      'dueDate',
      'aging',
      'createdAt',
      'updatedAt',
    ],
  },
};
