export const DEV_CONFIG = {
  APP_NAME: 'Lumigo',
  API_URL: 'http://localhost:3000',
  BASE_URL: 'http://localhost:3000',
  SHOW_DETAILED_ERRORS: true,
  NOTIFICATION: {
    DURATION: 5,
    PLACEMENT: 'topRight',
    PAUSE_ON_HOVER: false,
    CLASS_NAME: 'notify-wrapper',
  },
  AXIOS_DEFAULT_CONFIG: {
    TIMEOUT: 10000,
    RETRIES: 3,
    RETRY_DELAY: 1000,
    MAX_REFRESH_ATTEMPT: 3,
  },
  CONTEXT_MENU_CONFIG: {
    MINIMUM_SUB_MENU_SIZE: 190,
  },
  DATE_FORMATE_WITHOUT_TIME: 'DD/MM/YYYY',
  DATE_FORMATE_WITH_TIME_12H: 'DD/MM/YYYY hh:mm A',
  DATE_FORMATE_WITH_TIME_24H: 'DD/MM/YYYY HH:mm',
  DATE_TIME_SECOND_FORMATE_12H: 'DD/MM/YYYY hh:mm:ss A',
  DATE_TIME_SECOND_FORMATE_24H: 'DD/MM/YYYY HH:mm:ss',
  TIME_FORMATE_12H: 'hh:mm A',
  TIME_FORMATE_24H: 'HH:mm',
  MAX_PRICE_SET_SCHEDULE_LIMIT: 15,
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 16,
  OTP_LENGTH: 6,
  MAP_CENTER: {
    LAT: 45.508888,
    LNG: -73.561668,
  },
} as const;
