import React from 'react';
import NotificationManager from '@/lib/NotificationManager';
import { DEV_CONFIG } from '@/config/devConfig';

/**
 * Hook to create and use a NotificationManager instance
 * @returns {NotificationManager} An instance of NotificationManager
 */

export const useNotificationManager = (): NotificationManager => {
  return React.useMemo(
    () =>
      new NotificationManager({
        duration: DEV_CONFIG.NOTIFICATION.DURATION || 5,
        placement: (DEV_CONFIG.NOTIFICATION.PLACEMENT as any) || 'topRight',
        pauseOnHover: DEV_CONFIG.NOTIFICATION.PAUSE_ON_HOVER || false,
        className: DEV_CONFIG.NOTIFICATION.CLASS_NAME || 'notify-wrapper',
      }),
    []
  );
};

export const getNotificationManager = () => {
  return new NotificationManager({
    duration: DEV_CONFIG.NOTIFICATION.DURATION || 5,
    placement: (DEV_CONFIG.NOTIFICATION.PLACEMENT as any) || 'topRight',
    pauseOnHover: DEV_CONFIG.NOTIFICATION.PAUSE_ON_HOVER || false,
    className: DEV_CONFIG.NOTIFICATION.CLASS_NAME || 'notify-wrapper',
  });
};

const notificationManagerInstance = getNotificationManager();

export default notificationManagerInstance;
