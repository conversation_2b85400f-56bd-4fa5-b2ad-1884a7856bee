import { GetAddressDto } from '@/api/address/address.types';
import { IIsOpenModal } from '@/types/CommonTypes';
import { FormInstance } from 'antd';
import { AddressFieldConfig } from '@/lib/helper/customerSettingsHelper';

export interface IAddressOperationFormProps {
  form: FormInstance<GetAddressDto>;
  onFinish: (formValues: GetAddressDto) => Promise<void>;
  open: IIsOpenModal;
  cellData: GetAddressDto;
  fieldConfig?: AddressFieldConfig;
}
