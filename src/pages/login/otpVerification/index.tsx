import { AuthLayout } from '@/components/layout/AuthLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { Form, Button, Typography, Input } from 'antd';
import './otpVerification.css';
import '../loginPage.css';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';
import { logger } from '@/lib/logger/logger';
import { createVerification, verifyOTP } from '@/api/auth/auth.service';
import { useParams } from 'react-router-dom';
import { useState } from 'react';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import useThrottle from '@/hooks/useThrottle';
import { DEV_CONFIG } from '@/config/devConfig';

const { Text } = Typography;

const OTPVerificationPage = () => {
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const { navigate } = useNavigationContext();
  const { verificationId, email } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const notificationManager = useNotificationManager();

  const onFinish = async (values: { otp: string }) => {
    logger.info('OTP submitted:', values.otp);
    if (verificationId) {
      setIsLoading(true);
      const response = await verifyOTP({ verificationId, otp: values.otp });
      if (response) {
        setIsLoading(false);
        navigate(ROUTES.COMMON.RESET_PASSWORD.replace(':verificationId', `${verificationId}`));
      }
    }
  };

  const handleResendOtp = useThrottle(async () => {
    const response = await createVerification({
      verificationType: 'email',
      verificationOf: email || '',
    });

    if (response) {
      // navigate(`${ROUTES.COMMON.OTP_VERIFICATION}/${response.verificationId}`);
      notificationManager.success({
        message: t('common.success'),
        description: t('common.alert.otpResendSuccess', {
          email: email || '',
        }),
      });
      navigate(
        ROUTES.COMMON.OTP_VERIFICATION.replace(':email', `${email}`).replace(
          ':verificationId',
          `${response.verificationId}`
        )
      );
    }
  }, 3000);

  const validateOTP = (_: any, value: string) => {
    if (value.length !== DEV_CONFIG.OTP_LENGTH) {
      return Promise.reject(
        new Error(
          t('auth.otpVerificationPage.mustBeInLength', { length: String(DEV_CONFIG.OTP_LENGTH) })
        )
      );
    }
    return Promise.resolve();
  };

  return (
    <AuthLayout>
      <div className="flex justify-center items-center flex-col gap-4">
        <h1 className="text-[36px] font-semibold text-[#20363F]">
          {t('auth.otpVerificationPage.header')}
        </h1>
        <p className="text-[#647A83] text-center font-medium">
          {t('auth.otpVerificationPage.headerDescription', {
            length: String(DEV_CONFIG.OTP_LENGTH),
          })}{' '}
          <span className="text-primary-600"><EMAIL></span>
        </p>
      </div>
      <Form
        form={form}
        name="otpForm"
        onFinish={onFinish}
        className="otp-form mt-8 flex justify-center items-center flex-col"
      >
        <Form.Item
          name="otp"
          validateFirst
          rules={[
            { required: true, message: t('auth.otpVerificationPage.requireOtp') },
            { validator: validateOTP },
          ]}
        >
          <Input.OTP
            length={DEV_CONFIG.OTP_LENGTH}
            onInput={(value: string[]) => {
              const otpValue = value.join('');
              form.setFieldsValue({ otp: otpValue });
            }}
          />
        </Form.Item>
        <div className="text-center font-semibold text-[#647A83] mb-6 ">
          <Text type="secondary">
            {t('auth.otpVerificationPage.doNotGetOtp')}{' '}
            <Typography.Link onClick={handleResendOtp} className="!text-primary-600">
              {t('auth.otpVerificationPage.resendOtp')}
            </Typography.Link>
          </Text>
        </div>
        <Button
          type="primary"
          block
          htmlType="submit"
          className="h-[48px] w-full bg-primary-600 text-white !hover:bg-red-600 text-sm"
          loading={isLoading}
        >
          {t('auth.otpVerificationPage.verifyButton')}
        </Button>
      </Form>
    </AuthLayout>
  );
};

export default OTPVerificationPage;
