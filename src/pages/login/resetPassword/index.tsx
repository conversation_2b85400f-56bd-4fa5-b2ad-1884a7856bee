import { AuthLayout } from '@/components/layout/AuthLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { Form, Button, Input } from 'antd';
import { useState } from 'react';
import '../loginPage.css';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';
import Icon from '@ant-design/icons/lib/components/Icon';
import ListBulletIcon from '@/assets/icons/listBulletIcon';
import CompletedIcon from '@/assets/icons/completedIcon';
import { RuleObject } from 'antd/es/form';
import { formErrorRegex } from '@/constant/Regex';
import { logger } from '@/lib/logger/logger';
import { DEV_CONFIG } from '@/config/devConfig';
import { useParams } from 'react-router-dom';
import { resetPassword } from '@/api/auth/auth.service';

const ResetpasswordPage = () => {
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const { navigate } = useNavigationContext();
  const { verificationId } = useParams();

  const [passwordValidation, setPasswordValidation] = useState({
    minLength: false,
    hasNumberOrSymbol: false,
    hasUpperAndLowerCase: false,
  });

  const validatePassword = (password: string) => {
    const minLength = password.length >= 8;
    const hasNumberOrSymbol = formErrorRegex.ALLOW_NUMBER_AND_SYMBOL.test(password);
    const hasUpperAndLowerCase =
      formErrorRegex.ALLOW_LOWERCASE_ALPHABETS.test(password) &&
      formErrorRegex.ALLOW_UPPERCASE_ALPHABETS.test(password);

    setPasswordValidation({
      minLength,
      hasNumberOrSymbol,
      hasUpperAndLowerCase,
    });
  };

  const onPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validatePassword(e.target.value);
  };

  const onFinish = async (values: { password: string; confirmPassword: string }) => {
    logger.info('Password reset data submitted:', '', values);
    if (verificationId) {
      const response = await resetPassword({
        verificationId,
        password: values.password,
        confirmPassword: values.confirmPassword,
      });
      if (response) {
        navigate(ROUTES.COMMON.LOGIN);
      }
    }
  };

  const calculateWidth = () => {
    const length = Object.values(passwordValidation).filter(Boolean).length;
    return length > 0 ? (length / 3) * 100 : 10;
  };

  const getBarColor = () => {
    const length = Object.values(passwordValidation).filter(Boolean).length;

    if (length === 0 || length === 1) {
      return { color: 'red-500', text: t('auth.resetPasswordPage.passwordStatus.weak') };
    }
    if (length === 2) {
      return { color: 'yellow-500', text: t('auth.resetPasswordPage.passwordStatus.medium') };
    }
    if (length === 3) {
      return { color: 'green-500', text: t('auth.resetPasswordPage.passwordStatus.strong') };
    }

    return { color: 'gray-300', text: '' };
  };

  const passwordValidator = (_: RuleObject, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    const { minLength, hasNumberOrSymbol, hasUpperAndLowerCase } = passwordValidation;
    if (minLength && hasNumberOrSymbol && hasUpperAndLowerCase) {
      return Promise.resolve();
    }

    return Promise.reject();
  };

  return (
    <AuthLayout>
      <div className="flex justify-center items-center flex-col gap-4">
        <h1 className="text-[36px] font-semibold text-[#20363F]">
          {t('auth.resetPasswordPage.header')}
        </h1>
      </div>
      <Form form={form} name="resetPassword" onFinish={onFinish} className="auth-form mt-8">
        <Form.Item
          name="password"
          rules={[
            { required: true, message: t('auth.resetPasswordPage.requiredPassword') },
            {
              validator: passwordValidator,
            },
          ]}
          className="create-password"
        >
          <Input.Password
            placeholder={t('auth.resetPasswordPage.createPasswordPlaceholder')}
            onChange={onPasswordChange}
            maxLength={16}
          />
        </Form.Item>
        <Form.Item noStyle>
          {form.getFieldValue('password') && (
            <>
              <div className="w-full h-[3px] bg-gray-300 mt-2">
                <span
                  className={`block h-full bg-${getBarColor().color}`}
                  style={{
                    width: `${calculateWidth()}%`,
                  }}
                />
              </div>
              <span className={`text-[12px] mt-[5px] font-normal text-${getBarColor().color}`}>
                {' '}
                {getBarColor().text}
              </span>
            </>
          )}
          <div className="font-medium text-[#647A83] mb-6 mt-3 text-[12px] flex-col flex gap-[7px]">
            <div
              className={`flex gap-[5px] items-center ${
                passwordValidation.minLength ? 'text-green-500' : ''
              }`}
            >
              <Icon component={passwordValidation.minLength ? CompletedIcon : ListBulletIcon} />
              {t('auth.resetPasswordPage.minChar')}
            </div>
            <div
              className={`flex gap-[5px] items-center ${
                passwordValidation.hasNumberOrSymbol ? 'text-green-500' : ''
              }`}
            >
              <Icon
                component={passwordValidation.hasNumberOrSymbol ? CompletedIcon : ListBulletIcon}
              />
              {t('auth.resetPasswordPage.numberOrSymbol')}
            </div>
            <div
              className={`flex gap-[5px] items-center ${
                passwordValidation.hasUpperAndLowerCase ? 'text-green-500' : ''
              }`}
            >
              <Icon
                component={passwordValidation.hasUpperAndLowerCase ? CompletedIcon : ListBulletIcon}
              />
              {t('auth.resetPasswordPage.upperLowerCase')}
            </div>
          </div>
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          rules={[
            { required: true, message: t('auth.resetPasswordPage.requiredConfirmPassword') },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t('auth.resetPasswordPage.doNotMatchPassword')));
              },
            }),
          ]}
        >
          <Input
            placeholder={t('auth.resetPasswordPage.confirmPasswordPlaceholder')}
            maxLength={DEV_CONFIG.MAX_PASSWORD_LENGTH}
          />
        </Form.Item>
        <Button
          type="primary"
          block
          htmlType="submit"
          className="h-[48px] w-full mt-2 bg-primary-600 text-white !hover:bg-red-600 text-sm"
        >
          {t('auth.resetPasswordPage.resetPasswordButton')}
        </Button>
      </Form>
    </AuthLayout>
  );
};

export default ResetpasswordPage;
