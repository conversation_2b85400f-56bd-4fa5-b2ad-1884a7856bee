import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { useLanguage } from '@/hooks/useLanguage';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { blobToUrlNavigation, downloadFromBlob, getPaginationData } from '@/lib/helper';
import ColumnManage from '@/components/specific/columnManage';
import Icon, { LoadingOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import CustomModal from '@/components/common/modal/CustomModal';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { defaultPagination } from '@/constant/generalConstant';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import { PayInvoiceIcon } from '@/assets/icons/payInvoiceIcon';
import { invoicesHook, invoicesService } from '@/api/invoices/useInvoices';
import {
  IInvoiceListingDto,
  InvoiceStatus,
  IResponseInvoiceDto,
} from '@/api/invoices/invoices.types';
import dayjs from 'dayjs';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { ICellRendererParams } from 'ag-grid-community';
import CheckoutForm from '../orderEntry/payments/CheckoutForm';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { EyeIcon } from '@/assets';
import { AdvanceFilterColDefs } from '@/components/specific/searchFilter/searchFilter.types';
import { DEV_CONFIG } from '@/config/devConfig';

//TODO:move to .env
const stripePromise = loadStripe('pk_test_TYooMQauvdEDq54NiTphI7jx');

const InvoicePage = () => {
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const { t } = useLanguage();
  const [isModalOpen, setIsModalOpen] = useState<{
    isOpen: boolean;
    invoice: IResponseInvoiceDto | null;
  }>({ isOpen: false, invoice: null });
  const {
    data: customerInvoices,
    // refetch,
    isLoading,
    isFetching,
  } = invoicesHook.useList(filterParams, { staleTime: 30000 });

  const [invoices, setInvoices] = useState<IInvoiceListingDto[]>();
  const notificationManager = useNotificationManager();
  const gridRef = useRef<AgGridReact<IInvoiceListingDto>>(null);
  const [isDownloadLoading, setIsDownloadLoading] = useState<{
    isLoading: boolean;
    for: null | string;
  }>({ isLoading: false, for: null });

  useEffect(() => {
    if (customerInvoices) {
      setInvoices(customerInvoices.data);
    }
  }, [customerInvoices]);

  const paginationData = useMemo(() => getPaginationData(customerInvoices), [customerInvoices]);

  const downloadInvoiceHandler = useCallback(
    async (invoice: IResponseInvoiceDto) => {
      try {
        if (invoice.id) {
          setIsDownloadLoading({ isLoading: true, for: invoice.id });
          const response = await invoicesService.getById<Blob>(`${invoice.id}/preview`, undefined, {
            responseType: 'blob',
          });
          const url = `${invoice.invoiceNumber}.pdf`;
          downloadFromBlob(response, url);
        } else {
          notificationManager.error({
            message: t('common.error'),
            description: 'Failed to download invoice',
          });
        }
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: 'Failed to download invoice',
        });
      } finally {
        setIsDownloadLoading({ isLoading: false, for: null });
      }
    },
    [notificationManager, t]
  );

  const viewInvoiceHandler = useCallback(
    async (invoice: IResponseInvoiceDto) => {
      try {
        if (invoice.id) {
          const response = await invoicesService.getById<Blob>(`${invoice.id}/preview`, undefined, {
            responseType: 'blob',
          });
          blobToUrlNavigation(response);
        } else {
          notificationManager.error({
            message: t('common.error'),
            description: 'invoice id not found',
          });
        }
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: 'Something went wrong',
        });
      }
    },
    [notificationManager, t]
  );

  const customerInvoiceContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: 'View',
        key: 'viewInvoice',
        icon: EyeIcon as React.ElementType,
        onClick: (params) => viewInvoiceHandler(params.rowData),
      },
      {
        label: 'Download',
        key: 'downloadInvoice',
        icon: DownloadIcon as React.ElementType,
        onClick: (params) => downloadInvoiceHandler(params.rowData),
      },
      {
        label: 'Pay',
        icon: PayInvoiceIcon as React.ElementType,
        key: 'payInvoice',
        onClick: (params) => setIsModalOpen({ isOpen: true, invoice: params.rowData }),
      },
    ];
  }, [downloadInvoiceHandler, viewInvoiceHandler]);

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.invoice.sortable.includes(field);
  }, []);

  const invoiceStatusCellRenderer = useCallback((params: ICellRendererParams) => {
    const { value: status, data } = params;
    const today = dayjs().startOf('day');

    const isOverdue =
      data?.dueDate &&
      dayjs(data.dueDate).isBefore(today) &&
      status !== InvoiceStatus.Paid &&
      status !== InvoiceStatus.Void;

    // Centralized config
    const statusMap: Record<string, { className: string; label: string }> = {
      [InvoiceStatus.Draft]: { className: 'grey-chip', label: 'Draft' },
      [InvoiceStatus.Sent]: { className: 'primary-chip', label: 'Sent' },
      [InvoiceStatus.Paid]: { className: 'success-chip', label: 'Paid' },
      [InvoiceStatus.PartialPaid]: { className: 'warning-chip', label: 'Partially paid' },
      [InvoiceStatus.Void]: { className: 'error-chip', label: 'Void invoice' },
      Overdue: { className: 'error-chip', label: 'Overdue' },
      PaymentFailed: { className: 'error-chip', label: 'Payment failed' },
    };

    // Overdue check overrides everything
    const display = isOverdue
      ? { className: 'error-chip', label: `${status} - Overdue` }
      : statusMap[status];

    if (!display) return status;

    return (
      <div className="flex justify-center">
        <span className={`${display.className} block w-full`}>{display.label}</span>
      </div>
    );
  }, []);

  const invoiceColDefs: IColDef[] = useMemo(() => {
    return [
      {
        headerName: 'Invoice Number',
        field: 'invoiceNumber',
        visible: true,
        unSortIcon: isColumnSortable('invoiceNumber'),
        sortable: isColumnSortable('invoiceNumber'),
        flex: 1,
        type: 'string',
        minWidth: 200,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        headerName: 'Status',
        flex: 1,
        field: 'status',
        visible: true,
        type: 'dropdown',
        unSortIcon: isColumnSortable('status'),
        sortable: isColumnSortable('status'),
        cellRenderer: invoiceStatusCellRenderer,
        minWidth: 200,
      },
      {
        headerName: 'Created',
        field: 'invoiceDate',
        visible: true,
        type: 'date',
        unSortIcon: isColumnSortable('invoiceDate'),
        sortable: isColumnSortable('invoiceDate'),
        flex: 1,
        minWidth: 200,
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value
            ? dateFormatter(params.value, DEV_CONFIG.DATE_FORMATE_WITHOUT_TIME)
            : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        headerName: 'Due Date',
        field: 'dueDate',
        visible: true,
        unSortIcon: isColumnSortable('dueDate'),
        sortable: isColumnSortable('dueDate'),
        flex: 1,
        type: 'date',
        minWidth: 200,
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value
            ? dateFormatter(params.value, DEV_CONFIG.DATE_FORMATE_WITHOUT_TIME)
            : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        headerName: 'Total Amount',
        field: 'totalAmount',
        visible: true,
        unSortIcon: isColumnSortable('totalAmount'),
        sortable: isColumnSortable('totalAmount'),
        type: 'number',
        flex: 1,
        minWidth: 200,
        cellRenderer: (params: ICellRendererParams) => {
          const value = params?.value ? `$${params.value}` : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        headerName: 'Aging',
        field: 'aging',
        visible: true,
        unSortIcon: true,
        sortable: isColumnSortable('aging'),
        flex: 1,
        minWidth: 200,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        headerName: 'Action',
        field: 'action',
        visible: true,
        sortable: false,
        resizable: false,
        flex: 1,
        pinned: 'right',
        maxWidth: 80,
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="flex justify-center gap-2 h-full">
              {isDownloadLoading.isLoading && isDownloadLoading.for === params.data.id ? (
                <LoadingOutlined />
              ) : (
                <Icon
                  component={DownloadIcon}
                  className="cursor-pointer"
                  alt="view"
                  value={'view'}
                  onClick={() => downloadInvoiceHandler(params.data)}
                />
              )}
              {params.data.status !== InvoiceStatus.Paid && (
                <Icon
                  component={PayInvoiceIcon}
                  className="cursor-pointer"
                  alt="payInvoice"
                  value={'payInvoice'}
                  onClick={() => setIsModalOpen({ isOpen: true, invoice: params.data })}
                />
              )}
            </div>
          );
        },
        width: 90,
      },
    ];
  }, [
    downloadInvoiceHandler,
    invoiceStatusCellRenderer,
    isColumnSortable,
    isDownloadLoading.for,
    isDownloadLoading.isLoading,
    searchText,
  ]);

  const advanceFilterColdDefs: AdvanceFilterColDefs[] = useMemo(() => {
    return invoiceColDefs.map((col) => {
      if (col.field === 'status') {
        return {
          ...col,
          dropDownOptions: [
            { value: 'Draft', label: 'Draft' },
            { value: 'Sent', label: 'Sent' },
            { value: 'Paid', label: 'Paid' },
            { value: 'PartialPaid', label: 'Partial Paid' },
            { value: 'Void', label: 'Void' },
            { value: 'Refunded', label: 'Refunded' },
          ],
        };
      }
      return col;
    }) as AdvanceFilterColDefs[];
  }, [invoiceColDefs]);

  const closeModalHandler = useCallback(() => {
    setIsModalOpen({ isOpen: false, invoice: null });
  }, []);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="rounded-lg border-[#96A9B1]" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="invoice-payment-form"
          htmlType="submit"
          type="primary"
          loading={isFetching || isLoading}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {'Proceed to Pay'}
        </Button>
      </footer>
    ),
    [closeModalHandler, isFetching, isLoading, t]
  );

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.customerPortalInvoiceGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  return (
    <>
      <CustomModal
        modalTitle={'Pay Now'}
        modalDescription={
          isModalOpen.invoice?.invoiceNumber || 'Fill the card details to pay the invoice.'
        }
        open={isModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        destroyOnClose
        keyboard={false}
        maskClosable={false}
      >
        <Elements
          stripe={stripePromise}
          options={{
            appearance: {
              theme: 'stripe',
              variables: {
                colorPrimary: '#0570de',
                colorBackground: '#ffffff',
                colorText: '#30313d',
              },
            },
          }}
        >
          <CheckoutForm isStripeRequired wrapperClassName="!p-0" />
        </Elements>
      </CustomModal>
      <div className={`h-[93%] px-5`}>
        <header className="flex justify-between items-center gap-3">
          <div className=" text-[#090A1A] font-semibold text-3xl self-end">{'Invoices'}</div>
          <div className="flex justify-end items-center gap-3">
            <SearchFilterComponent
              colDefs={advanceFilterColdDefs}
              isSetQuickFilter={false}
              searchInputPlaceholder={'Search Invoices'}
              onSearch={searchHandler}
              onFilterApply={applyFilters}
              setSelectedQuickFilterData={setSelectedQuickFilterData}
              supportedFields={filterableModules.invoice.advanceFilter}
              clearAllFunctionRef={clearAllFunctionRef}
              setFilterParams={setFilterParams}
              gridName={GridNames.customerPortalInvoiceGrid}
            />
            <ColumnManage colDefs={invoiceColDefs} gridName={GridNames.customerPortalInvoiceGrid} />
          </div>
        </header>
        <main
          className={`${selectedQuickFilterData.length > 0 ? 'overflow-y-hidden h-[95%]' : 'h-full'} overflow-x-hidden overflow-y-auto bg-white`}
        >
          <ActiveFilters
            selectedQuickFilterData={selectedQuickFilterData}
            clearAllToDefault={clearAllToDefault}
            colDefs={invoiceColDefs}
            className={'pt-5'}
          />
          <div className="mx-auto h-full flex justify-center items-center pt-3">
            <CustomAgGrid
              rowData={invoices}
              gridRef={gridRef}
              columnDefs={invoiceColDefs}
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
              loading={isFetching || isLoading}
              isContextMenu
              contextMenuItem={customerInvoiceContextMenuItems}
              className={`3xsm:!h-[62vh] md:!h-[72vh] ${selectedQuickFilterData.length > 0 ? 'lg:!h-[69vh]' : 'lg:!h-[74vh]'}`}
              gridName={GridNames.customerPortalInvoiceGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : 'No Invoices Found',
                description: '',
              }}
            />
          </div>
        </main>
      </div>
    </>
  );
};

export default InvoicePage;
