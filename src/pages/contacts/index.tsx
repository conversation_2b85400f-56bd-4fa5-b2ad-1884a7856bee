import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { AgGridReact } from 'ag-grid-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { But<PERSON>, Divider, Form } from 'antd';
import Icon from '@ant-design/icons';
import { DeleteIcon, deleteSvg, EmailCredentialIcon, EyeIcon, PlusButtonIcon } from '@/assets';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { CellClickedEvent, CellContextMenuEvent, ICellRendererParams } from 'ag-grid-community';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import CustomModal from '@/components/common/modal/CustomModal';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { useCallback } from 'react';
import ContactFormOperations from './contactsOperations';
import { IIsOpenModal, ValuesObject } from './contactsOperations/contact.types';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { useParams } from 'react-router-dom';
import { defaultPagination } from '@/constant/generalConstant';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { getPaginationData, onSortChangeHandler } from '@/lib/helper/agGridHelper';
import {
  ICustomerContact,
  IPermissionsForContacts,
  ICategories,
} from '@/api/contacts/contacts.types';
import { customerCategoryHook } from '@/api/customerCategory/useCustomerCategories';
import { contactsHook } from '@/api/contacts/useContacts';
import { ICustomerCategoriesResponse } from '@/api/customerCategory/customerCategories.types';
import useThrottle from '@/hooks/useThrottle';

const ContactsPage = () => {
  const gridRef = useRef<AgGridReact<ICustomerContact>>(null);
  const [addContactsForm] = Form.useForm();
  const [allContacts, setAllContacts] = useState<ICustomerContact[]>();
  const [initialData, setInitialData] = useState<ICustomerContact>();
  const notificationManager = useNotificationManager();
  const [cellData, setCellData] = useState<CellContextMenuEvent<ICellRendererParams>>(
    {} as CellContextMenuEvent<ICellRendererParams>
  );
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [searchTerm, setSearchText] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<ValuesObject[]>([]);
  const [contactCategories, setCustomerCategories] = useState<ValuesObject[]>([]);
  const { data: contactCategoriesList } =
    customerCategoryHook.useList<ICustomerCategoriesResponse[]>();

  useEffect(() => {
    if (contactCategoriesList) {
      const formattedCategoriesList = contactCategoriesList?.map((item) => {
        return {
          label: item?.name,
          value: item?.id,
          id: item?.id,
        };
      });
      setCustomerCategories(formattedCategoriesList);
    }
  }, [contactCategoriesList]);

  const { t } = useLanguage();
  const { id } = useParams<{ id: string }>();

  const {
    data: customersContactList,
    refetch,
    isLoading,
  } = contactsHook.useList(filterParams, { staleTime: 30000 });

  const [isAddContactModalOpen, setIsAddContactModalOpen] = useState<IIsOpenModal>({
    isOpen: false,
    isEdit: false,
  });
  const handleContactView = useCallback(
    (data: ICellRendererParams | CellClickedEvent) => {
      const categoriesFormat = data?.data?.categories?.map((item: any) => {
        return { value: item?.id, label: item?.name };
      });
      setInitialData({ ...data.data, categories: categoriesFormat } as ICustomerContact);

      addContactsForm.setFieldsValue({
        ...data.data,
        isActive: data.data.isActive,
        address: data.data.permissions.address,
        prices: data.data.permissions.prices,
        invoice: data.data.permissions.invoices,
        categories: categoriesFormat,
      });

      setIsAddContactModalOpen({ isOpen: true, isEdit: true });
    },
    [addContactsForm]
  );

  useEffect(() => {
    if (customersContactList) {
      setAllContacts(customersContactList?.data as ICustomerContact[]);
    }
  }, [customersContactList]);

  const handleDeleteContact = contactsHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.columns.contactDeletedSuccessfully'),
      });
      refetch();
      customAlert.destroy();
    },
  });
  const contactDeleteConfirmation = useCallback(
    async (data: ICellRendererParams) => {
      customAlert.error({
        title: t('dashboard.customer.columns.deleteContact'),
        message: t('dashboard.customer.columns.confirmDeleteContact'),
        firstButtonFunction: async () => {
          await handleDeleteContact.mutateAsync(`${data.data.id}`);
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [handleDeleteContact, t]
  );
  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.contact.sortable.includes(field);
  }, []);

  const customerColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('zonePage.colDefs.name'),
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,

        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'phoneNumber',
        headerName: t('addressPage.colDefs.phone'),
        sortable: isColumnSortable('phoneNumber'),
        unSortIcon: isColumnSortable('phoneNumber'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'email',
        headerName: t('auth.email'),
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'status',
        headerName: t('dashboard.customer.columns.status'),
        sortable: isColumnSortable('status'),
        unSortIcon: isColumnSortable('status'),
        type: 'boolean',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.isActive ? (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-[seagreen]  flex" />
              {t('dashboard.customer.columns.active')}
            </div>
          ) : (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-error-500  flex" />
              {t('dashboard.customer.columns.inactive')}
            </div>
          );
        },
      },
      {
        field: 'invoice',
        headerName: t('sidebar.invoiceAccess'),
        sortable: isColumnSortable('invoice'),
        unSortIcon: isColumnSortable('invoice'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.invoices ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'prices',
        headerName: t('sidebar.pricesAccess'),
        sortable: isColumnSortable('price'),
        unSortIcon: isColumnSortable('price'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.prices ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'locations',
        headerName: t('sidebar.addressAccess'),
        sortable: isColumnSortable('location'),
        unSortIcon: isColumnSortable('location'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.address ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'action',
        headerName: t('zonePage.colDefs.action'),
        pinned: 'right',
        width: 80,
        maxWidth: 80,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="flex gap-2 h-full items-center">
              <Icon
                component={() => <EyeIcon bool={false} />}
                onClick={() => handleContactView(params)}
                className="cursor-pointer"
                alt="view"
                value={'view'}
              />

              {!params.data?.isPrimary && (
                <Icon
                  onClick={() => contactDeleteConfirmation(params)}
                  component={DeleteIcon}
                  className="cursor-pointer"
                  alt="delete"
                />
              )}
            </div>
          );
        },
        visible: true,
      },
    ];
  }, [t, isColumnSortable, searchTerm, handleContactView, contactDeleteConfirmation]);

  const createContactMutation = contactsHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.contactCreatedSuccessfully'),
      });
      refetch();
    },
  });
  const updateContactMutation = contactsHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.contactUpdatedSuccessfully'),
      });
      refetch();
    },
  });

  const onFinish = useThrottle(async () => {
    const data = addContactsForm.getFieldsValue();
    const dataToSendFormat = {
      name: data.name as string,
      email: data.email as string,
      phoneCountryCode: data.phoneCountryCode as string,
      phoneNumber: data.phoneNumber as string,
      phoneExtension: data.phoneExtension as string,
      permissions: {
        prices: data.prices,
        address: data.address,
        invoices: data.invoice,
      } as IPermissionsForContacts,

      categories: selectedCategories as unknown as ICategories[],
      userId: id as string,
      isActive: (data.isActive as boolean) || false,
    };

    if (isAddContactModalOpen.isEdit) {
      await updateContactMutation.mutateAsync({
        id: `${initialData?.id}`,
        data: dataToSendFormat,
      });
    } else {
      await createContactMutation.mutateAsync(dataToSendFormat);
    }

    closeModalHandler();
    addContactsForm.resetFields();
  }, 3000);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const closeModalHandler = useCallback(() => {
    setIsAddContactModalOpen({ isOpen: false, isEdit: false });
    addContactsForm.resetFields();
    setInitialData({} as ICustomerContact);
  }, [addContactsForm]);

  const paginationData = useMemo(
    () => getPaginationData(customersContactList),
    [customersContactList]
  );

  const updateMutation = contactsHook.useUpdate({
    onSuccess: () => {
      const data = cellData as unknown as ICellRendererParams;
      notificationManager.success({
        message: t('common.success'),
        description: t('common.alert.credentialSent', {
          email: data?.data?.email,
        }),
      });
    },
  });

  const handleResendCredentials = async () => {
    const data = cellData as unknown as ICellRendererParams;
    await updateMutation.mutateAsync({
      id: `${data?.data?.id}/sendContactCredentials`,
    });
  };

  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('dashboard.customer.emailLoginDetails'),
        icon: EmailCredentialIcon as React.ElementType,
        key: 'emailLoginDetails',
        onClick: () => {
          handleResendCredentials();
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
        onClick: () => contactDeleteConfirmation(cellData as unknown as ICellRendererParams),
      },
    ];
  }, [cellData, contactDeleteConfirmation, t]);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full">
        <div className="w-1/3 flex">
          {isAddContactModalOpen.isEdit && (
            <Button
              onClick={handleResendCredentials}
              className="hover:!text-black hover:!border-gray-400 border-gray-400"
            >
              {t('dashboard.customer.columns.resendCredentials')}
            </Button>
          )}
        </div>
        <div className="flex justify-end gap-3 w-2/3">
          <Button
            onClick={closeModalHandler}
            className="hover:!text-black hover:!border-gray-400 border-gray-400"
          >
            {t('common.cancel')}
          </Button>
          <Button
            disabled={initialData?.isPrimary}
            form="add-contact-form"
            htmlType="submit"
            type="primary"
            loading={isLoading}
            className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
          >
            {isAddContactModalOpen.isEdit ? t('common.update') : t('common.save')}
          </Button>
        </div>
      </footer>
    ),
    [
      closeModalHandler,
      handleResendCredentials,
      initialData?.isPrimary,
      isAddContactModalOpen.isEdit,
      isLoading,
      t,
    ]
  );
  return (
    <>
      <CustomModal
        maskClosable={false}
        modalTitle={
          isAddContactModalOpen.isEdit
            ? t('dashboard.customer.columns.updateContact')
            : t('dashboard.customer.columns.addContact')
        }
        modalDescription={t('dashboard.customer.columns.enterNewContactDetails')}
        open={isAddContactModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        destroyOnClose
        keyboard={false}
      >
        <CustomDivider label={t('zonePage.basicDetails')} />
        <ContactFormOperations
          form={addContactsForm}
          onFinish={onFinish}
          currentData={initialData as ICustomerContact}
          isAddContactModalOpen={isAddContactModalOpen}
          setSelectedCategories={setSelectedCategories}
          contactCategories={contactCategories}
        />
      </CustomModal>
      <div className="flex h-full">
        <div className="flex-1 flex flex-col overflow-hidden bg-white px-4">
          <div className="flex 3xsm:text-center md:flex-row justify-between w-2/3 md:gap-4 3xsm:w-full 3xsm:flex-col sm:flex-row gap-0 sm:gap-2">
            <span className="text-[#090A1A] font-semibold text-3xl self-start sm:self-end">
              {t('contacts.title')}
            </span>
            <div className="flex gap-3">
              <div className="flex gap-3">
                <SearchFilterComponent
                  onSearch={searchHandler}
                  colDefs={customerColDefs}
                  advanceFilter={false} // TODO: As of now, we don't need advance filter hear
                  searchInputPlaceholder={t('dashboard.customer.columns.searchContact')}
                  gridName={GridNames.customerPortalContactGrid}
                />
                <ColumnManage
                  colDefs={customerColDefs}
                  gridName={GridNames.customerPortalContactGrid}
                />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
              </div>
              <div className="pt-5">
                <Button
                  className="h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!border-primary-600 hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                  loading={isLoading}
                  onClick={() => setIsAddContactModalOpen({ isOpen: true, isEdit: false })}
                >
                  {t('dashboard.customer.columns.addContact')}
                </Button>
              </div>
            </div>
          </div>
          <main className="h-full overflow-x-hidden  bg-white">
            <div className="mx-auto pt-3">
              <CustomAgGrid
                gridRef={gridRef}
                gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
                loading={isLoading}
                rowData={allContacts}
                columnDefs={customerColDefs}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                className="3xsm:!h-[62vh] md:!h-[74.5vh]"
                gridName={GridNames.customerPortalContactGrid}
                isContextMenu
                contextMenuItem={customerContextMenuItems}
                onContextMenu={(params: CellContextMenuEvent<ICellRendererParams>) =>
                  setCellData(params)
                }
                onCellClicked={(params: CellClickedEvent) => handleContactView(params)}
                emptyState={{
                  title: searchTerm
                    ? t('common.noMatchesFound')
                    : t('dashboard.customer.contactsEmptyState.title'),
                  description: searchTerm
                    ? ''
                    : t('dashboard.customer.contactsEmptyState.description'),
                  link: searchTerm ? '' : t('dashboard.customer.contactsEmptyState.link'),
                  onLinkAction: () => setIsAddContactModalOpen({ isOpen: true, isEdit: false }),
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default ContactsPage;
