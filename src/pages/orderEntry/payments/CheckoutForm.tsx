import { formErrorRegex } from '@/constant/Regex';
import {
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { Form, Input, Select } from 'antd';
import { Dispatch, FormEvent, SetStateAction, useEffect, useState } from 'react';
interface ICheckOutPageProps {
  setIsAllFilledUp?: Dispatch<SetStateAction<boolean>>;
  isStripeRequired: boolean;
  wrapperClassName?: string;
}
const CheckoutForm: React.FC<ICheckOutPageProps> = (props) => {
  const { setIsAllFilledUp, isStripeRequired } = props;
  const stripe = useStripe();
  const elements = useElements();
  const [message, setMessage] = useState('');
  const [name, setName] = useState('');
  const [country, setCountry] = useState('US');
  const [postalCode, setPostalCode] = useState('');
  const [postalCodeError, setPostalCodeError] = useState('');
  const [formValues, setFormValues] = useState<any>({
    cardNumber: { error: '', value: false },
    cardExpiry: { error: '', value: false },
    cardCvc: { error: '', value: false },
  });

  const validatePostalCode = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    const isValid = formErrorRegex.PostalCode.test(value);
    if (!isValid) {
      setPostalCode('');
      setPostalCodeError('Please enter a valid postal code (e.g., A1A 1A1)');
    } else {
      setPostalCode(value);
      setPostalCodeError(''); // clear error when valid
    }
  };
  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setMessage('Processing payment...');

    try {
      const { error } = await stripe.createPaymentMethod({
        type: 'card',
        card: elements.getElement(CardNumberElement)!,
        billing_details: {
          name: name,
          address: {
            country: country,
            postal_code: postalCode,
          },
        },
      });

      if (error) {
        setMessage(error.message || 'An error occurred');
        return;
      }
      setMessage('Payment successful! Thank you for your order.');
    } catch (err) {
      setMessage('An unexpected error occurred.');
      console.error('Payment error:', err);
    }
  };
  useEffect(() => {
    if (!isStripeRequired) {
      setIsAllFilledUp && setIsAllFilledUp(true);
      return;
    }
    if (formValues) {
      if (
        formValues.cardNumber.value &&
        formValues.cardExpiry.value &&
        formValues.cardCvc.value &&
        name !== '' &&
        postalCode !== ''
      ) {
        setIsAllFilledUp && setIsAllFilledUp(true);
      } else {
        setIsAllFilledUp && setIsAllFilledUp(false);
      }
    }
  }, [formValues, postalCode, name, isStripeRequired, setIsAllFilledUp]);

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#30313d',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    },
  };
  //   console.log(errors, 'errors');
  // setFormValues({ ...formValues, cardNumber: { error: e.error?.message, value: e.complete } })
  return (
    <Form onFinish={handleSubmit} className={`space-y-4 p-[16px] ${props.wrapperClassName || ''}`}>
      <div className="mb-4 ">
        <label className="block text-sm font-medium text-gray-700 mb-1">Card information</label>
        <div className="border rounded-md p-3 mb-1">
          <CardNumberElement
            onChange={(e) => {
              setFormValues({
                ...formValues,
                cardNumber: { error: e.error?.message || '', value: e.complete },
              });
            }}
            options={{ ...cardElementOptions, disabled: !isStripeRequired }}
          />
        </div>
        {formValues.cardNumber.error && (
          <div className="text-red-500 text-md">{formValues.cardNumber.error}</div>
        )}

        <div className="flex space-x-2 mt-1">
          <div className="flex flex-col w-1/2">
            <div className=" border rounded-md p-3">
              <CardExpiryElement
                onChange={(e) => {
                  setFormValues({
                    ...formValues,
                    cardExpiry: { error: e.error?.message || '', value: e.complete },
                  });
                }}
                options={{ ...cardElementOptions, disabled: !isStripeRequired }}
              />
            </div>
            {formValues.cardExpiry.error && (
              <div className="text-red-500 text-md">{formValues.cardExpiry.error}</div>
            )}
          </div>
          <div className="w-1/2 border rounded-md p-3">
            <CardCvcElement
              onChange={(e) => {
                setFormValues({
                  ...formValues,
                  cardCvc: { error: e.error?.message || '', value: e.complete },
                });
              }}
              options={{ ...cardElementOptions, disabled: !isStripeRequired }}
            />
          </div>
          {formValues.cardCvc.error && (
            <div className="text-red-500 text-md">{formValues.cardCvc.error}</div>
          )}
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Name on card</label>
        <Input
          disabled={!isStripeRequired}
          type="text"
          value={name}
          maxLength={250}
          onChange={(e) => setName(e.target.value)}
          className="w-full border rounded-md p-3 focus:!shadow-none focus:!border-[#e5e7eb] hover:!shadow-none hover:!border-[#e5e7eb]"
          placeholder="Full name on card"
        />
      </div>

      <div className="mb-4 flex flex-col gap-2">
        <label className="block text-sm font-medium text-gray-700 mb-1">Country or region</label>
        <Select
          disabled={!isStripeRequired}
          value={country}
          onChange={(e) => setCountry(e)}
          className="w-full h-[44px] region-payment-class"
        >
          <option value="US">United States</option>
          <option value="CA">Canada</option>
          <option value="GB">United Kingdom</option>
          <option value="AU">Australia</option>
        </Select>

        <Input
          disabled={!isStripeRequired}
          type="text"
          defaultValue={postalCode}
          onChange={validatePostalCode}
          className={`w-full p-3 focus:!shadow-none focus:!border-[#e5e7eb] hover:!shadow-none hover:!border-[#e5e7eb] ${postalCodeError ? 'border-red-500' : ''}`}
          placeholder="ZIP / Postal code"
        />
        {postalCodeError && <div className="text-red-500 text-sm mt-1">{postalCodeError}</div>}
      </div>

      {message && <div className="mt-4 text-green-600">{message}</div>}
    </Form>
  );
};

export default CheckoutForm;
