import { DateCalendarIcon } from '@/assets';
import { Form, DatePicker, Row, Col } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { FormInstance } from 'antd/lib';
import { OrderStatus } from '@/pages/orders/orderDetails/OrderStatusHistory';
import { useParams } from 'react-router-dom';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import { DEV_CONFIG } from '@/config/devConfig';
import { TimeFormats } from '@/api/tenantSettings/tenantSettings.types';

interface ITimingFormProps {
  form: FormInstance;
  setSelectedDateAndTime: Dispatch<SetStateAction<string>>;
  isAbleToEdit: boolean;
  orderSelectedTime: any;
  orderDetails?: any;
}

const TimingForm: React.FC<ITimingFormProps> = (props) => {
  const { id } = useParams();
  const { setSelectedDateAndTime, isAbleToEdit, form, orderSelectedTime, orderDetails } = props;
  const [currentTime, setCurrentTime] = useState('');

  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  const dateTimeFormate = useMemo(
    () =>
      tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
        ? DEV_CONFIG.DATE_TIME_SECOND_FORMATE_12H
        : DEV_CONFIG.DATE_TIME_SECOND_FORMATE_24H,
    [tenantSettings?.globalConfiguration?.timeFormat]
  );

  const updateCurrentTime = useCallback(() => {
    const now = dayjs();
    setCurrentTime(now.format(dateTimeFormate));
  }, [dateTimeFormate]);

  useEffect(() => {
    updateCurrentTime();
    const intervalId = setInterval(updateCurrentTime, 1000);
    return () => clearInterval(intervalId);
  }, [tenantSettings?.globalConfiguration?.timeFormat, updateCurrentTime]);

  useEffect(() => {
    if (orderSelectedTime) {
      form.setFieldsValue({
        pickupDate: dayjs(orderSelectedTime),
      });
    } else {
      const currentDateTime = dayjs();
      form.setFieldsValue({
        pickupDate: currentDateTime,
      });
      setSelectedDateAndTime(currentDateTime.utc().toISOString());
    }
  }, [orderSelectedTime, form, setSelectedDateAndTime]);

  return (
    <Form form={form} layout="vertical" requiredMark={false} className="timing-form">
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="timing-form-item"
            label="Pickup date and time"
            name="pickupDate"
            rules={[
              { required: true, message: 'Pickup date and time is required' },
              {
                validator: (_, value) => {
                  if (!value) {
                    return Promise.resolve();
                  }

                  const selectedDateTime = dayjs(value);
                  const currentDateTime = dayjs();

                  if (
                    selectedDateTime.isBefore(currentDateTime) &&
                    ((orderDetails && orderDetails.status === OrderStatus.Draft) || !id)
                  ) {
                    return Promise.reject(new Error('Pickup time cannot be in the past'));
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker
              value={orderSelectedTime ? dayjs(orderSelectedTime) : dayjs()}
              disabled={!isAbleToEdit}
              suffixIcon={<DateCalendarIcon />}
              showTime
              format={dateTimeFormate}
              disabledDate={(currentDate) => {
                return currentDate && currentDate < dayjs().startOf('day');
              }}
              disabledTime={(current) => {
                const now = dayjs();
                if (current.isSame(now, 'day')) {
                  return {
                    disabledHours: () => {
                      const hours = [];
                      for (let i = 0; i < now.hour(); i++) {
                        hours.push(i);
                      }
                      return hours;
                    },
                    disabledMinutes: (selectedHour) => {
                      if (selectedHour === now.hour()) {
                        const minutes = [];
                        for (let i = 0; i < now.minute(); i++) {
                          minutes.push(i);
                        }
                        return minutes;
                      }
                      return [];
                    },
                  };
                }
                return {};
              }}
              placeholder={'Select date and time'}
              className="timing-form-item w-full h-[40px]"
              onOk={(time) => {
                const selectedDateTime = dayjs(time);
                const currentDateTime = dayjs();

                if (
                  selectedDateTime.isBefore(currentDateTime) &&
                  ((orderDetails && orderDetails.status === OrderStatus.Draft) || !id)
                ) {
                  form.setFields([
                    {
                      name: 'pickupDate',
                      errors: ['Pickup time cannot be in the past'],
                    },
                  ]);
                  return;
                }

                setSelectedDateAndTime(time.utc().toISOString());
              }}
              onChange={(date) => {
                if (date) {
                  const selectedDateTime = dayjs(date);
                  const currentDateTime = dayjs();

                  if (
                    selectedDateTime.isBefore(currentDateTime) &&
                    ((orderDetails && orderDetails.status === OrderStatus.Draft) || !id)
                  ) {
                    form.setFields([
                      {
                        name: 'pickupDate',
                        errors: ['Pickup time cannot be in the past'],
                      },
                    ]);
                    return;
                  }

                  setSelectedDateAndTime(date.utc().toISOString());
                }
              }}
              dropdownClassName="orders-general-datepicker-dropdown"
            />
          </Form.Item>
        </Col>

        <div className="p-8 flex gap-3">
          <span className="font-[500] text-[16px] text-primary-900">Current date & time:</span>
          <span className="font-[600] text-[18px] text-primary-900 font-inter">{currentTime}</span>
        </div>
      </Row>
    </Form>
  );
};

export default TimingForm;
