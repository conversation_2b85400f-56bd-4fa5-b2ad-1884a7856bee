import { Form, Select, InputNumber, Row, Col, Button, Input, message, Divider } from 'antd';
import { useState, useEffect, useMemo, Fragment } from 'react';
import './addressForms.css';
import { EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { DeleteIcon } from '@/assets';
import { useLanguage } from '@/hooks/useLanguage';
import { numberFieldValidator } from '@/lib/FormValidators';
import { FormInstance, UploadFile } from 'antd/lib';
import { IPackage } from '@/api/packages/packages.types';
import { UploadChangeParam } from 'antd/es/upload';
import { IPackages } from '@/api/order/order.types';
import { ICreateFileUploadResponse } from '@/api/fileUpload/fileUpload.types';
import { fileUploadServiceHook } from '@/api/fileUpload/useFileUploads';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import { AddressFieldConfig } from '@/lib/helper/customerSettingsHelper';
import { getFieldConfig, createValidationRules } from '@/lib/helper';

const MAX_TOTAL_SIZE_MB = 5;
const MAX_TOTAL_SIZE_BYTES = MAX_TOTAL_SIZE_MB * 1024 * 1024;
interface IPackageFormDetailsForm {
  setPackageFormDetails: (details: IPackages[]) => void;
  packageList: IPackage[];
  form: FormInstance;
  orderDetails: any;
  isEditMode?: boolean; // Add this prop to explicitly control edit vs create mode
  isAbleToEdit: boolean;
  fieldConfig?: AddressFieldConfig;
}
const PackageDetailsForm: React.FC<IPackageFormDetailsForm> = (props) => {
  const { setPackageFormDetails, packageList, form, orderDetails, isAbleToEdit, fieldConfig } = props;
  const { t } = useLanguage();
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [isInitialForm, setIsInitialForm] = useState<boolean>(true);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [isSelectedPackagePrimary, setIsSelectedPackagePrimary] = useState(false);
  const [showNewForm, setShowNewForm] = useState(true);

  const defaultPackageFieldConfig = useMemo<AddressFieldConfig>(() => ({
    packageType: { isVisible: true, isRequired: true },
    quantity: { isVisible: true, isRequired: true },
    totalWeight: { isVisible: true, isRequired: true },
    length: { isVisible: true, isRequired: true },
    width: { isVisible: true, isRequired: true },
    height: { isVisible: true, isRequired: true },
    declaredValue: { isVisible: true, isRequired: false },
    cubicDimension: { isVisible: true, isRequired: false },
    imageName: { isVisible: true, isRequired: false },
  }), []);

  const effectiveFieldConfig = useMemo<AddressFieldConfig>(
    () => ({
      ...defaultPackageFieldConfig,
      ...(fieldConfig || {}),
    }),
    [defaultPackageFieldConfig, fieldConfig]
  );

  const resolvedFieldConfig = useMemo(
    () => ({
      packageType: getFieldConfig(effectiveFieldConfig, 'packageType'),
      quantity: getFieldConfig(effectiveFieldConfig, 'quantity'),
      totalWeight: getFieldConfig(effectiveFieldConfig, 'totalWeight'),
      length: getFieldConfig(effectiveFieldConfig, 'length'),
      width: getFieldConfig(effectiveFieldConfig, 'width'),
      height: getFieldConfig(effectiveFieldConfig, 'height'),
      declaredValue: getFieldConfig(effectiveFieldConfig, 'declaredValue'),
      cubicDimension: getFieldConfig(effectiveFieldConfig, 'cubicDimension'),
      imageName: getFieldConfig(effectiveFieldConfig, 'imageName'),
    }),
    [effectiveFieldConfig]
  );

  const {
    packageType: packageTypeFieldConfig,
    quantity: quantityFieldConfig,
    totalWeight: totalWeightFieldConfig,
    length: lengthFieldConfig,
    width: widthFieldConfig,
    height: heightFieldConfig,
    declaredValue: declaredValueFieldConfig,
    cubicDimension: cubicDimensionFieldConfig,
    imageName: imageFieldConfig,
  } = resolvedFieldConfig;

  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  useEffect(() => {
    const formValues = form.getFieldsValue();
    if (
      !formValues.packages ||
      !Array.isArray(formValues.packages) ||
      formValues.packages.length === 0
    ) {
      setPackageFormDetails([]);
      return;
    }
    const formattedPackages = formValues.packages
      .filter((pkg: IPackages) => pkg && typeof pkg === 'object')
      .map((pkg: IPackages) => ({
        ...pkg,
        height: pkg?.height ? Number(pkg.height) : undefined,
        width: pkg?.width ? Number(pkg?.width) : undefined,
        length: pkg?.length ? Number(pkg?.length) : undefined,
        totalWeight: pkg?.totalWeight ? Number(pkg?.totalWeight) : undefined,
        quantity: pkg?.quantity ? Number(pkg?.quantity) : undefined,
        declaredValue: pkg?.declaredValue ? Number(pkg?.declaredValue) : undefined,
        packageType: pkg?.packageTemplateName,
      }));

    if (
      formattedPackages.length > 0 &&
      formattedPackages.some(
        (pkg: IPackage) =>
          pkg &&
          Object.values(pkg).some((value) => value !== undefined && value !== null && value !== '')
      )
    ) {
      setPackageFormDetails(formattedPackages);
    } else {
      setPackageFormDetails([]);
    }
  }, [form, packageList]);
  useEffect(() => {
    const formValues = form.getFieldsValue();
    if (
      !formValues.packages ||
      !Array.isArray(formValues.packages) ||
      formValues.packages.length === 0
    ) {
      setPackageFormDetails([]);
      return;
    }

    const formattedPackages = formValues.packages
      .filter((pkg: IPackages) => pkg && typeof pkg === 'object')
      .map((pkg: IPackages) => ({
        ...pkg,
        height: pkg?.height ? Number(pkg.height) : undefined,
        width: pkg?.width ? Number(pkg?.width) : undefined,
        length: pkg?.length ? Number(pkg?.length) : undefined,
        totalWeight: pkg?.totalWeight ? Number(pkg?.totalWeight) : undefined,
        quantity: pkg?.quantity ? Number(pkg?.quantity) : undefined,
        declaredValue: pkg?.declaredValue ? Number(pkg?.declaredValue) : undefined,
        packageType: pkg?.packageTemplateName,
      }));

    if (
      formattedPackages.length > 0 &&
      formattedPackages.some(
        (pkg: IPackage) =>
          pkg &&
          Object.values(pkg).some((value) => value !== undefined && value !== null && value !== '')
      )
    ) {
      setPackageFormDetails(formattedPackages);
    } else {
      setPackageFormDetails([]);
    }
  }, [form.getFieldValue('packages')]);

  const validateFiles = (_: any, value: any) => {
    if (!value || !value.fileList || value.fileList.length === 0) {
      return Promise.resolve();
    }
    if (value.fileList.length > 1) {
      return Promise.reject(new Error(t('orderEntryForms.address.onlySingleFileAllowed')));
    }
    const totalSize = value.fileList.reduce((sum: number, file: UploadFile) => {
      return sum + (file.originFileObj?.size || 0);
    }, 0);

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      return Promise.reject(
        new Error(
          t('orderEntryForms.packageDetails.errors.fileSizeExceeded', {
            size: (totalSize / (1024 * 1024)).toFixed(2),
            limit: MAX_TOTAL_SIZE_MB,
          })
        )
      );
    }

    return Promise.resolve();
  };

  const toggleEdit = (index: number) => {
    if (editingIndex === index) {
      setEditingIndex(null);
    } else {
      setEditingIndex(index);
    }
  };
  const updateCubicDimension = (index: number) => {
    const length = form.getFieldValue(['packages', index, 'length']);
    const width = form.getFieldValue(['packages', index, 'width']);
    const height = form.getFieldValue(['packages', index, 'height']);

    const cubicDimension = `${length || 'Length'} × ${width || 'Width'} × ${height || 'Height'}`;
    form.setFieldValue(['packages', index, 'cubicDimension'], cubicDimension);
  };

  const handleFileChange = (info: UploadChangeParam, fieldPath: string) => {
    const { file } = info;

    const totalSize = file.originFileObj?.size || 0;

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      message.warning(
        t('orderEntryForms.packageDetails.warnings.fileSizeExceeded', {
          size: (totalSize / (1024 * 1024))?.toFixed(2),
          limit: MAX_TOTAL_SIZE_MB,
        })
      );
    }

    form.validateFields([fieldPath]);
  };

  const packagesFormWatcher = Form.useWatch('packages', form);

  const handleFileDelete = (packageIndex: number) => {
    form.setFieldValue(['packages', packageIndex, 'imageUrl'], null);
    form.setFieldValue(['packages', packageIndex, 'imageName'], null);
    form.setFieldValue(['packages', packageIndex, 'imageShortName'], null);
  };

  const handlePackageTypeChange = (value: string, index: number) => {
    setEditingIndex(index);
    if (value) {
      const selectedPackage = packageList.find((item: IPackage) => item.id === value);
      if (selectedPackage) {
        const isPrimaryPackage = selectedPackage.metadata?.isPrimary === true;
        setIsSelectedPackagePrimary(isPrimaryPackage);
        // const currentValues = form.getFieldValue(['packages', index]);
        const prefilledValues = {
          packageType: selectedPackage?.name,
          packageTemplateId: selectedPackage?.id,
          packageTemplateName: selectedPackage?.name,
          length: selectedPackage.length,
          width: selectedPackage.width,
          height: selectedPackage.height,
          totalWeight:
            selectedPackage.totalWeight !== undefined &&
            selectedPackage.totalWeight !== null &&
            Number(selectedPackage.totalWeight),
          quantity: selectedPackage.quantity || 1,
          declaredValue: selectedPackage.declaredValue,
          cubicDimension: updateCubicDimension(index),
        };

        form.setFieldsValue({
          packages: {
            [index]: prefilledValues,
          },
        });

        if (prefilledValues.length && prefilledValues.width && prefilledValues.height) {
          const cubicDimension = `${prefilledValues.length} × ${prefilledValues.width} × ${prefilledValues.height}`;
          form.setFieldValue(['packages', index, 'cubicDimension'], cubicDimension);
        }

        form.setFields([
          {
            name: ['packages', index, 'length'],
            errors: [],
            validating: false,
          },
          {
            name: ['packages', index, 'width'],
            errors: [],
            validating: false,
          },
          {
            name: ['packages', index, 'height'],
            errors: [],
            validating: false,
          },
          {
            name: ['packages', index, 'totalWeight'],
            errors: [],
            validating: false,
          },
        ]);
      }
    }
  };

  const packageLength = form.getFieldValue('packages')?.length;
  useEffect(() => {
    setShouldScroll(packageLength > 5);
  }, [packageLength]);

  const packageHasValue = (index: number) => {
    const packages = form.getFieldValue('packages') || [];
    const packageAtGivenIndex = packages[index];
    if (
      !packageAtGivenIndex ||
      typeof packageAtGivenIndex !== 'object' ||
      !Object.values(packageAtGivenIndex).some(
        (value) => value !== undefined && value !== null && value !== ''
      )
    ) {
      return false;
    }

    const keyFields = [
      'packageType',
      'quantity',
      'totalWeight',
      'length',
      'width',
      'height',
      'declaredValue',
    ];

    return keyFields.some((field) => {
      const value = packageAtGivenIndex[field];
      return value !== undefined && value !== null && value !== '';
    });
  };

  useEffect(() => {
    if (packagesFormWatcher && Array.isArray(packagesFormWatcher)) {
      const packages = packagesFormWatcher;
      if (packages.length === 1) {
        setShowNewForm(true);
        setIsInitialForm(false);
        if (editingIndex === null) {
          setEditingIndex(0);
        }
      } else if (packages.length === 0) {
        setShowNewForm(true);
        setEditingIndex(null);
        setIsInitialForm(true);
      } else if (packages.length > 1) {
        setShowNewForm(true);
        setIsInitialForm(false);
        if (editingIndex === null || editingIndex >= packages.length) {
          setEditingIndex(null);
        }
      }
    }
  }, [packagesFormWatcher, editingIndex]);

  const uploadFileMutation = fileUploadServiceHook.useCreateByEntity<ICreateFileUploadResponse>(
    'upload',
    undefined,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  const customUploadRequest = async ({ file, onSuccess, onError }: any, index: number) => {
    const payload = {
      file: file,
      entityType: 'order-item',
      entityId: null,
      type: 'image',
    };

    try {
      const res = await uploadFileMutation.mutateAsync(payload);
      onSuccess?.(res);
      if (res.success) {
        form.setFieldValue(['packages', index, 'imageUrl'], res.url);
        form.setFieldValue(['packages', index, 'imageShortName'], res.filename);
        form.setFieldValue(['packages', index, 'imageName'], { file: { name: res.originalName } });
        form.setFieldValue(['packages', index, 'fileId'], res.fileId);
      }
    } catch (err) {
      onError?.(err);
      form.setFields([
        {
          name: ['packages', index, 'imageName'],
          errors: ['Failed to upload file'],
        },
      ]);
    }
  };

  return (
    <Form
      className="package-details-form"
      form={form}
      initialValues={{ packages: [{}] }}
      layout="vertical"
    >
      {
        <div
          className={shouldScroll ? 'max-h-[500px] overflow-y-auto pr-2' : ''}
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#d1d5db transparent',
          }}
        >
          <Form.List name="packages">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => {
                  const packages = form.getFieldValue('packages') || [];
                  const actualPackages = packages.filter(
                    (pkg: any) =>
                      pkg &&
                      typeof pkg === 'object' &&
                      Object.values(pkg).some(
                        (value) => value !== undefined && value !== null && value !== ''
                      )
                  );
                  let isEditing: boolean = false;
                  const isLast = index === actualPackages.length;

                  if (isLast && !showNewForm) {
                    return null;
                  }

                  isEditing =
                    editingIndex === index || (editingIndex === null && isLast && showNewForm);
                  const fieldPath: string = ['packages', index, 'imageName'] as unknown as string;

                  return (
                    <div key={field.key} className="mb-6 bg-white rounded-lg p-[24px] pb-[0px]">
                      {isLast &&
                        fields.length > 1 &&
                        showNewForm &&
                        (() => {
                          const hasMultiplePackages = actualPackages.length > 1;
                          const hasFormVisible = showNewForm && actualPackages.length >= 1;
                          const shouldShowRemove =
                            (hasMultiplePackages || hasFormVisible) && isEditing;
                          if (!showNewForm && actualPackages.length === 1) {
                            return false;
                          }

                          return shouldShowRemove;
                        })() && (
                          <div className="flex justify-end mb-2">
                            <Button
                              danger
                              icon={<DeleteIcon />}
                              type="text"
                              onClick={() => {
                                if (fields.length > 1) {
                                  remove(index);
                                  setShowNewForm(false);
                                  setEditingIndex(null);
                                }
                              }}
                              className="text-error-600 p-2 border border-error-500 rounded-md"
                            >
                              {t('common.buttons.remove')}
                            </Button>
                          </div>
                        )}

                      <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center">
                          {(index !== fields.length ||
                            editingIndex === null ||
                            editingIndex === index) && (
                            <span className="border border-primary-600 rounded-md text-primary-600 text-sm font-medium px-3 py-1">
                              {t('orderEntryForms.packageDetails.package')} {index + 1}
                            </span>
                          )}
                        </div>
                        {!isEditing && !isInitialForm && index !== actualPackages.length && (
                          <div className="flex items-center gap-3">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              onClick={() => toggleEdit(index)}
                              className="mr-2 text-[#333] border border-primary-100 rounded-md"
                            />
                            {(() => {
                              const packages = form.getFieldValue('packages') || [];
                              const actualPackages = packages.filter(
                                (pkg: any) =>
                                  pkg &&
                                  typeof pkg === 'object' &&
                                  Object.values(pkg).some(
                                    (value) => value !== undefined && value !== null && value !== ''
                                  )
                              );

                              const shouldShowRemove =
                                actualPackages.length > 1 ||
                                (actualPackages.length === 1 &&
                                  index === actualPackages.length + 1);
                              return shouldShowRemove ? (
                                <Button
                                  disabled={fields.length === 0}
                                  danger
                                  icon={<DeleteIcon />}
                                  type="text"
                                  onClick={() => {
                                    if (fields.length > 1) {
                                      remove(index);
                                      setEditingIndex(null);
                                      setShowNewForm(false);

                                      // const newLastIndex = fields.length - 2;
                                      // setEditingIndex(newLastIndex >= 0 ? newLastIndex : 0);
                                    }
                                  }}
                                  className="text-error-600 p-2 border border-error-500 rounded-md"
                                >
                                  {t('common.buttons.remove')}
                                </Button>
                              ) : null;
                            })()}
                          </div>
                        )}
                        {isEditing &&
                          editingIndex === index &&
                          index !== actualPackages.length &&
                          actualPackages.length > 1 && (
                            <div className="flex items-center gap-3">
                              <Button
                                type="text"
                                onClick={() => toggleEdit(index)}
                                className="text-[#333] border border-primary-100 rounded-md"
                              >
                                {t('common.buttons.cancel')}
                              </Button>
                              <Button
                                type="text"
                                onClick={() => toggleEdit(index)}
                                className="text-[#333] bg-primary-600 text-white border border-primary-600 rounded-md"
                              >
                                {t('common.buttons.update')}
                              </Button>
                            </div>
                          )}
                      </div>

                      {isEditing ? (
                        <>
                          <Row gutter={16} className="mb-4">
                            {packageTypeFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'packageType']}
                                  fieldKey={[field.fieldKey as number, 'packageType']}
                                  label={
                                    <span className="text-sm font-medium">
                                      {t('orderEntryForms.packageDetails.packagingType')}
                                    </span>
                                  }
                                  rules={
                                    packageTypeFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            packageTypeFieldConfig,
                                            t(
                                              'orderEntryForms.packageDetails.errors.packageTypeRequired'
                                            )
                                          ),
                                        ]
                                      : []
                                  }
                                >
                                  <Select
                                    placeholder={
                                      packageTypeFieldConfig.placeholder ||
                                      t('orderEntryForms.packageDetails.selectPackageType')
                                    }
                                    className="address-select-item"
                                    options={packageList}
                                    onChange={(value) => handlePackageTypeChange(value, index)}
                                  />
                                </Form.Item>
                              </Col>
                            )}
                            {quantityFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'quantity']}
                                  fieldKey={[field.fieldKey as number, 'quantity']}
                                  label={
                                    <span className="text-sm font-medium">
                                      {t('orderEntryForms.packageDetails.quantity')}
                                    </span>
                                  }
                                  rules={
                                    quantityFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            quantityFieldConfig,
                                            t(
                                              'orderEntryForms.packageDetails.errors.quantityRequired'
                                            )
                                          ),
                                          {
                                            validator: (_, value) => {
                                              if (value > 99999) {
                                                return Promise.reject(
                                                  new Error(t('priceModifiers.maximumValueExceeded'))
                                                );
                                              } else if (value < 0) {
                                                return Promise.reject(
                                                  new Error(
                                                    t(
                                                      'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                                    )
                                                  )
                                                );
                                              }
                                              return Promise.resolve();
                                            },
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <InputNumber
                                    maxLength={5}
                                    className="address-select-item"
                                    min={1}
                                    placeholder={quantityFieldConfig.placeholder || '1'}
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}
                          </Row>

                          <Row gutter={16} className="mb-4">
                            {cubicDimensionFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  rules={
                                    cubicDimensionFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            cubicDimensionFieldConfig,
                                            'Dimensions are required'
                                          ),
                                        ]
                                      : []
                                  }
                                  dependencies={['length', 'width', 'height']}
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'cubicDimension']}
                                  fieldKey={[field.fieldKey as number, 'cubicDimension']}
                                  label={
                                    <span className="text-sm font-medium">
                                      {t('orderEntryForms.packageDetails.cubicDimension')}
                                    </span>
                                  }
                                >
                                  <Input
                                    className="address-select-item"
                                    placeholder={
                                      cubicDimensionFieldConfig.placeholder ||
                                      t('orderEntryForms.packageDetails.dimensionsPlaceholder')
                                    }
                                    disabled
                                  />
                                </Form.Item>
                              </Col>
                            )}

                            {totalWeightFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'totalWeight']}
                                  fieldKey={[field.fieldKey as number, 'totalWeight']}
                                  label={
                                    <span className="text-sm font-medium">
                                      {t('orderEntryForms.packageDetails.combinedWeight')}{' '}
                                      <span className="text-[14px] font-[400] text-primary-200">
                                        ({tenantSettings?.globalConfiguration?.weightUnit})
                                      </span>
                                    </span>
                                  }
                                  rules={
                                    totalWeightFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            totalWeightFieldConfig,
                                            t(
                                              'orderEntryForms.packageDetails.errors.weightRequired'
                                            )
                                          ),
                                          {
                                            validator: (_, value) => {
                                              if (isSelectedPackagePrimary) {
                                                return Promise.resolve();
                                              }
                                              if (value > 99999) {
                                                return Promise.reject(
                                                  new Error(t('priceModifiers.maximumValueExceeded'))
                                                );
                                              } else if (value < 0) {
                                                return Promise.reject(
                                                  new Error(
                                                    t(
                                                      'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                                    )
                                                  )
                                                );
                                              }
                                              return Promise.resolve();
                                            },
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <InputNumber
                                    maxLength={5}
                                    className="address-select-item"
                                    min={1}
                                    step={0.1}
                                    placeholder={
                                      totalWeightFieldConfig.placeholder || '0.0'
                                    }
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}
                          </Row>

                          <Row gutter={16} className="mb-4">
                            {lengthFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'length']}
                                  fieldKey={[field.fieldKey as number, 'length']}
                                  label={
                                    <>
                                      <span className="text-[14px] font-[400] text-primary-200">
                                        ({tenantSettings?.globalConfiguration?.dimensionUnit})
                                      </span>{' '}
                                      <span className="text-sm font-medium">
                                        {t('orderEntryForms.packageDetails.length')}
                                      </span>
                                    </>
                                  }
                                  rules={
                                    lengthFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            lengthFieldConfig,
                                            t('orderEntryForms.packageDetails.errors.lengthRequired')
                                          ),
                                          {
                                            validator: (_, value) => {
                                              if (isSelectedPackagePrimary) {
                                                return Promise.resolve();
                                              }
                                              if (value < 0) {
                                                return Promise.reject(
                                                  new Error(
                                                    t(
                                                      'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                                    )
                                                  )
                                                );
                                              }
                                              return Promise.resolve();
                                            },
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <InputNumber
                                    step={0.01}
                                    maxLength={5}
                                    className="address-select-item"
                                    min={1}
                                    placeholder={lengthFieldConfig.placeholder || '0'}
                                    onChange={() => {
                                      updateCubicDimension(index);
                                    }}
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}

                            {widthFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'width']}
                                  fieldKey={[field.fieldKey as number, 'width']}
                                  label={
                                    <>
                                      <span className="text-[14px] font-[400] text-primary-200">
                                        ({tenantSettings?.globalConfiguration?.dimensionUnit})
                                      </span>
                                      {''}
                                      <span className="text-sm font-medium">
                                        {t('orderEntryForms.packageDetails.width')}
                                      </span>
                                    </>
                                  }
                                  rules={
                                    widthFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            widthFieldConfig,
                                            t('orderEntryForms.packageDetails.errors.widthRequired')
                                          ),
                                          {
                                            validator: (_, value) => {
                                              if (isSelectedPackagePrimary) {
                                                return Promise.resolve();
                                              }
                                              if (value < 0) {
                                                return Promise.reject(
                                                  new Error(
                                                    t(
                                                      'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                                    )
                                                  )
                                                );
                                              }
                                              return Promise.resolve();
                                            },
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <InputNumber
                                    step={0.1}
                                    maxLength={6}
                                    className="address-select-item"
                                    min={1}
                                    placeholder={widthFieldConfig.placeholder || '0'}
                                    onChange={() => {
                                      updateCubicDimension(index);
                                    }}
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}
                          </Row>

                          <Row gutter={16} className="mb-4">
                            {heightFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'height']}
                                  fieldKey={[field.fieldKey as number, 'height']}
                                  label={
                                    <>
                                      <span className="text-[14px] font-[400] text-primary-200">
                                        ({tenantSettings?.globalConfiguration?.dimensionUnit})
                                      </span>{' '}
                                      <span className="text-sm font-medium">
                                        {t('orderEntryForms.packageDetails.height')}
                                      </span>
                                    </>
                                  }
                                  rules={
                                    heightFieldConfig.isVisible
                                      ? [
                                          ...createValidationRules(
                                            heightFieldConfig,
                                            t('orderEntryForms.packageDetails.errors.heightRequired')
                                          ),
                                          {
                                            validator: (_, value) => {
                                              if (isSelectedPackagePrimary) {
                                                return Promise.resolve();
                                              }
                                              if (value < 0) {
                                                return Promise.reject(
                                                  new Error(
                                                    t(
                                                      'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                                    )
                                                  )
                                                );
                                              }
                                              return Promise.resolve();
                                            },
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <InputNumber
                                    step={0.1}
                                    maxLength={6}
                                    className="address-select-item"
                                    min={1}
                                    placeholder={heightFieldConfig.placeholder || '0'}
                                    onChange={() => {
                                      updateCubicDimension(index);
                                    }}
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}

                            {declaredValueFieldConfig.isVisible && (
                              <Col xs={24} md={12}>
                                <Form.Item
                                  className="address-form-item"
                                  {...field}
                                  name={[field.name, 'declaredValue']}
                                  fieldKey={[field.fieldKey as number, 'declaredValue']}
                                  label={
                                    <span className="text-sm font-medium">
                                      {t('orderEntryForms.packageDetails.declaredValue')}
                                    </span>
                                  }
                                  rules={
                                    declaredValueFieldConfig.isVisible
                                      ? createValidationRules(
                                          declaredValueFieldConfig,
                                          t('orderEntryForms.packageDetails.declaredValue')
                                        )
                                      : []
                                  }
                                >
                                  <InputNumber
                                    maxLength={6}
                                    className="address-select-item"
                                    min={1}
                                    addonBefore="$"
                                    placeholder={
                                      declaredValueFieldConfig.placeholder || '0.00'
                                    }
                                    precision={2}
                                    onKeyDown={(e) =>
                                      numberFieldValidator(e, { allowDecimals: true })
                                    }
                                  />
                                </Form.Item>
                              </Col>
                            )}
                          </Row>

                          <Row gutter={16}>
                            {imageFieldConfig.isVisible && (
                              <Col span={24}>
                                <Form.Item
                                  dependencies={[orderDetails]}
                                  className="address-form-item !mb-0"
                                  {...field}
                                  name={[field.name, 'imageName']}
                                  fieldKey={[field.fieldKey as number, 'imageName']}
                                  label={
                                    <div className="flex items-center">
                                      <span className="text-sm font-medium mr-1">
                                        {t('orderEntryForms.packageDetails.uploadImage')}
                                      </span>
                                      <InfoCircleOutlined className="text-gray-400" />
                                    </div>
                                  }
                                  rules={
                                    imageFieldConfig.isVisible
                                      ? [
                                          {
                                            validator: validateFiles,
                                          },
                                          ...createValidationRules(
                                            imageFieldConfig,
                                            t('orderEntryForms.packageDetails.uploadImage')
                                          ),
                                        ]
                                      : []
                                  }
                                  validateTrigger={['onChange', 'onBlur']}
                                >
                                  <CustomUpload
                                    label={' '}
                                    form={form}
                                    name={fieldPath}
                                    placeholder={
                                      imageFieldConfig.placeholder ||
                                      t('orderEntryForms.packageDetails.uploadImagePlaceholder')
                                    }
                                    uploadButtonText={t('common.buttons.upload')}
                                    uploadComponentProps={{
                                      maxCount: 1,
                                      accept: 'image/*',
                                      customRequest: (options) =>
                                        customUploadRequest(options, index),
                                      multiple: false,
                                      listType: 'picture',
                                      onChange: (info) => handleFileChange(info, fieldPath),
                                      showUploadList: false,
                                    }}
                                  />
                                </Form.Item>
                              </Col>
                            )}
                          </Row>

                          <div className="mt-2">
                            <div className="mt-2">
                              {imageFieldConfig.isVisible && packagesFormWatcher?.[index]?.imageUrl && (
                                <div className="flex flex-wrap gap-2">
                                  <div className="relative w-16 h-16 border rounded p-1">
                                    <div
                                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center cursor-pointer"
                                      onClick={() => handleFileDelete(index)}
                                    >
                                      ×
                                    </div>
                                    <img
                                      src={
                                        packagesFormWatcher?.[index]?.imageUrl instanceof File
                                          ? URL.createObjectURL(
                                              packagesFormWatcher?.[index]?.imageUrl
                                            )
                                          : (packagesFormWatcher?.[index]?.imageUrl as string)
                                      }
                                      alt={`preview ${index}`}
                                      className="w-full h-full object-contain"
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </>
                      ) : (
                        !isEditing &&
                        editingIndex !== index &&
                        index !== actualPackages.length && (
                          <div className="border-0 p-0">
                            {(() => {
                              const summaryItems = [
                                {
                                  key: 'packageType',
                                  visible: packageTypeFieldConfig.isVisible,
                                  label: t('orderEntryForms.packageDetails.packageType'),
                                  value:
                                    form.getFieldValue(['packages', index, 'packageType']) || '-',
                                },
                                {
                                  key: 'quantity',
                                  visible: quantityFieldConfig.isVisible,
                                  label: t('orderEntryForms.packageDetails.quantity'),
                                  value:
                                    form.getFieldValue(['packages', index, 'quantity']) || '-',
                                },
                                {
                                  key: 'cubicDimension',
                                  visible: cubicDimensionFieldConfig.isVisible,
                                  label: t('orderEntryForms.packageDetails.cubicDimension'),
                                  value:
                                    form.getFieldValue(['packages', index, 'cubicDimension']) || '-',
                                },
                                {
                                  key: 'totalWeight',
                                  visible: totalWeightFieldConfig.isVisible,
                                  label: (
                                    <>
                                      {t('orderEntryForms.packageDetails.combinedWeight')}{' '}
                                      <span className="text-[14px] font-[400] text-gray-500">
                                        ({tenantSettings?.globalConfiguration?.weightUnit})
                                      </span>
                                    </>
                                  ),
                                  value:
                                    form.getFieldValue(['packages', index, 'totalWeight']) || '-',
                                },
                              ].filter((item) => item.visible);

                              if (summaryItems.length === 0) {
                                return null;
                              }

                              return (
                                <Row gutter={[16, 16]}>
                                  {summaryItems.map((item, itemIndex) => (
                                    <Fragment key={item.key}>
                                      <Col span={5}>
                                        <div className="text-[16px] font-[600]">{item.label}</div>
                                        <div className="text-[16px]">{item.value}</div>
                                      </Col>
                                      {itemIndex !== summaryItems.length - 1 && (
                                        <Divider
                                          type="vertical"
                                          className="h-8 !bg-primmay-100"
                                        />
                                      )}
                                    </Fragment>
                                  ))}
                                </Row>
                              );
                            })()}
                            <div className="mt-2">
                              {imageFieldConfig.isVisible && packagesFormWatcher?.[index]?.imageUrl && (
                                <div className="flex flex-wrap gap-2">
                                  <div className="relative w-16 h-16 border rounded p-1">
                                    <img
                                      src={
                                        packagesFormWatcher?.[index]?.imageUrl instanceof File
                                          ? URL.createObjectURL(
                                              packagesFormWatcher?.[index]?.imageUrl
                                            )
                                          : (packagesFormWatcher?.[index]?.imageUrl as string)
                                      }
                                      alt={`preview ${index}`}
                                      className="w-full h-full object-contain"
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      )}
                      {index !== fields.length - 1 && <Divider className="!bg-primmay-100" />}
                    </div>
                  );
                })}

                <div className="w-full bg-primary-25 p-3 flex justify-end mt-4">
                  <Button
                    disabled={!isAbleToEdit}
                    type="primary"
                    onClick={() => {
                      setIsInitialForm(false);
                      if (!showNewForm) {
                        setShowNewForm(true);
                      } else {
                        const lastIndex = fields.length - 1;
                        const lastPackage = form.getFieldValue(['packages', lastIndex]);
                        if (
                          !lastPackage ||
                          (lastPackage &&
                            typeof lastPackage === 'object' &&
                            !Object.values(lastPackage).some(
                              (value) => value !== undefined && value !== null && value !== ''
                            ))
                        ) {
                          setShowNewForm(true);
                        }

                        form
                          .validateFields(
                            [
                              packageTypeFieldConfig.isVisible
                                ? ['packages', lastIndex, 'packageType']
                                : null,
                              quantityFieldConfig.isVisible
                                ? ['packages', lastIndex, 'quantity']
                                : null,
                              totalWeightFieldConfig.isVisible
                                ? ['packages', lastIndex, 'totalWeight']
                                : null,
                              lengthFieldConfig.isVisible
                                ? ['packages', lastIndex, 'length']
                                : null,
                              widthFieldConfig.isVisible
                                ? ['packages', lastIndex, 'width']
                                : null,
                              heightFieldConfig.isVisible
                                ? ['packages', lastIndex, 'height']
                                : null,
                              cubicDimensionFieldConfig.isVisible
                                ? ['packages', lastIndex, 'cubicDimension']
                                : null,
                              imageFieldConfig.isVisible
                                ? ['packages', lastIndex, 'imageName']
                                : null,
                            ].filter((item): item is (string | number)[] => item !== null)
                          )
                          .then(() => {
                            const lastIndex = fields.length - 1;
                            const hasValue = packageHasValue(lastIndex);

                            if (hasValue) {
                              add();
                              setEditingIndex(fields.length);
                            } else {
                              setEditingIndex(lastIndex);
                            }

                            setShowNewForm(true);
                          })
                          .catch((err) => {
                            console.error('Validation failed:', err);
                          });
                      }
                    }}
                    className="bg-[#2E3A8C] text-white hover:!bg-[#2E3A8C] hover:opacity-90 rounded-md p-2 py-4"
                  >
                    {t('orderEntryForms.packageDetails.addMorePackage')}
                  </Button>
                </div>
              </>
            )}
          </Form.List>
        </div>
      }
    </Form>
  );
};

export default PackageDetailsForm;
