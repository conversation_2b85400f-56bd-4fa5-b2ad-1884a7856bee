import React from 'react';
import { Typography, Card } from 'antd';

const { Text } = Typography;

interface PaymentSummaryProps {
  subTotal?: number;
  additionalServicesTotal?: number;
  showPriceIncludingTaxes?: boolean;
  gstAmount?: number;
  qstAmount?: number;
  total?: number;
}

const PaymentSummary: React.FC<PaymentSummaryProps> = ({
  subTotal,
  additionalServicesTotal,
  showPriceIncludingTaxes = false,
  gstAmount,
  qstAmount,
  total,
}) => {
  return (
    <div className="payment-summary-container">
      <Card
        title={
          <div className="payment-summary-header">
            <span>Payment summary</span>
          </div>
        }
        className="payment-summary-card"
        bordered={true}
      >
        <div className="payment-summary-content bg-primary-25">
          <div className="flex justify-between py-1">
            <Text className="font-medium">Sub total</Text>
            <Text className="font-semibold">${subTotal?.toFixed(2) ?? '0.00'}</Text>
          </div>
          <div className="flex justify-between py-1">
            <Text className="font-medium">Additional services</Text>
            <Text className="font-semibold">${additionalServicesTotal?.toFixed(2) ?? '0.00'}</Text>
          </div>
          {showPriceIncludingTaxes && (
            <>
              <div className="flex justify-between py-1">
                <Text className="font-medium">GST Tax</Text>
                <Text className="font-semibold">${gstAmount?.toFixed(2) ?? '0.00'}</Text>
              </div>
              <div className="flex justify-between py-1">
                <Text className="font-medium">PST Tax</Text>
                <Text className="font-semibold">${qstAmount?.toFixed(2) ?? '0.00'}</Text>
              </div>
            </>
          )}
          <div className="border-t border-gray-200 my-2"></div>
          <div className="flex justify-between py-1">
            <Text className="font-bold text-lg">Total</Text>
            <Text className="font-bold text-lg">${total?.toFixed(2) ?? '0.00'}</Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PaymentSummary;
