import { Form, Input, Row, Col, Switch, Typography, InputNumber, Select } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormInstance } from 'antd/lib';
import { vehicleTypeServiceHook } from '@/api/vehicleTypes/useVehicleTypes';

const { TextArea } = Input;
const { Text } = Typography;

interface IAdditionalDetailsFormProps {
  form: FormInstance;
  isAbleToEdit: boolean;
}

const AdditionalDetailsForm: React.FC<IAdditionalDetailsFormProps> = (props) => {
  const { form, isAbleToEdit } = props;
  const { t } = useLanguage();
  const [availableVehicleTypes, setAvailableVehicleTypes] = useState<
    {
      label: string;
      value: string;
      id: string;
    }[]
  >([]);

  const { data: vehicleTypes } = vehicleTypeServiceHook.useEntities('no-pagination');
  const showCashOnDelivery = Form.useWatch('isCod', form);

  useEffect(() => {
    if (vehicleTypes?.data) {
      const allVehicleTypesFormat = vehicleTypes?.data?.map((item) => {
        return {
          label: item?.name,
          value: item?.id,
          id: item?.id,
        };
      });

      setAvailableVehicleTypes(allVehicleTypesFormat);
    }
  }, [vehicleTypes?.data]);
  return (
    <Form form={form} layout="vertical" requiredMark={false} className="additional-details-form">
      <div className="mb-4">
        <Row gutter={16} className="mb-4">
          <Col xs={24} md={12}>
            <div className="flex w-[98%] justify-between items-center mb-2">
              <div className="flex items-center">
                <Text>Cash on delivery</Text>
                <InfoCircleOutlined className="ml-1 text-gray-400" />
              </div>
              <Form.Item name={'isCod'} noStyle>
                <Switch disabled={!isAbleToEdit} className="order-collapse-switch " />
              </Form.Item>
            </div>
            {showCashOnDelivery && (
              <Form.Item
                className="address-form-item"
                name={'amount'}
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value) return;
                      if (value > 999999999) {
                        return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                      } else if (value < 0) {
                        return Promise.reject(
                          new Error(t('priceModifiers.configureTiersForm.pleaseEnterValidNumber'))
                        );
                      } else {
                        return Promise.resolve();
                      }
                    },
                  },
                ]}
              >
                <InputNumber
                  disabled={!isAbleToEdit}
                  maxLength={9}
                  className="address-select-item"
                  min={0}
                  addonBefore="$"
                  placeholder="0.00"
                  precision={2}
                />
              </Form.Item>
            )}
          </Col>
        </Row>

        <div className="mb-2">
          <Text>Delivery Instruction</Text>
        </div>
        <Form.Item name="specialInstruction" noStyle>
          <TextArea
            placeholder="Entrance is on the left side of the building"
            autoSize={{ minRows: 3, maxRows: 6 }}
            className="rounded-md w-full"
          />
        </Form.Item>
      </div>
      <Form.Item className="w-[50%]" label="Select vehicle type" name="vehicleTypeId">
        <Select
          className="h-[48px]"
          options={availableVehicleTypes}
          placeholder="Select Vehicle Type"
        />
      </Form.Item>
    </Form>
  );
};

export default AdditionalDetailsForm;
