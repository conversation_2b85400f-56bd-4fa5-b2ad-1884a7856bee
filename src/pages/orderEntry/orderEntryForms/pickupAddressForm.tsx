import { Form, Input, Select, Row, Col, Space, InputRef } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import './addressForms.css';
import { formErrorRegex } from '@/constant/Regex';
import { MaskedInput } from 'antd-mask-input';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Dispatch, SetStateAction, useCallback, useMemo, useRef, useState } from 'react';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { GetAddressDto } from '@/api/address/address.types';
import TextArea from 'antd/es/input/TextArea';
import { FormInstance } from 'antd/lib';
import { Autocomplete } from '@react-google-maps/api';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { isFormChangedHand<PERSON>, getFieldConfig, createValidationRules } from '@/lib/helper';
import { AddressFieldConfig } from '@/lib/helper/customerSettingsHelper';
import { AxiosError } from 'axios';
import { zoneService } from '@/api/zones/useZones';
import { TrackedError } from '@/types/AxiosTypes';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';

interface IPickupAddressFormProps {
  selectedPickupAddress: GetAddressDto | null;
  refetch: () => void;
  isRefetching: boolean;
  form: FormInstance;
  setIsFieldChanged: Dispatch<SetStateAction<any>>;
  isFieldChanged: any;
  isAbleToEdit: boolean;
  fieldConfig?: AddressFieldConfig;
}

const PickupAddressForm: React.FC<IPickupAddressFormProps> = (props) => {
  const { selectedPickupAddress, form, setIsFieldChanged, isFieldChanged, isAbleToEdit, fieldConfig } = props;
  const { t } = useLanguage();
  
  const inputPhoneRef = useRef<InputRef>(null);

  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    if (focus) {
      inputPhoneRef.current?.focus();
      setMaskPhoneInput(selectedCountryMask);
    }
  }, []);
  const phoneNumberPrefix = (
    <Form.Item className="general-form-item-prefix !h-[38px]" name={'phoneCountryCode'}>
      <Select
      disabled={!isAbleToEdit}
        className="address-select-item"
        placeholder={t('common.usa')}
        options={optionsForPrefix}
        onChange={(value) => maskingInputPhone(value)}
      />
    </Form.Item>
  );
  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();

  const autocompleteRef = useRef<Autocomplete>(null);

  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);
  const onPlaceChanged = async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);

      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
        latitude: selectedPlaceData?.latitude,
        longitude: selectedPlaceData?.longitude,
      };
      form.setFieldsValue(newFormValues);
      try {
        if (selectedPlaceData?.postalCode) {
          const trimmedPostalCode = selectedPlaceData?.postalCode.split(' ')[0];
          await zoneService.getById(`${trimmedPostalCode}`);
          await form.validateFields(['addressLine1']);
        } else {
          form.setFields([
            {
              name: 'addressLine1',
              errors: [t('orderEntryForms.address.noPostalCodeProvided')],
            },
          ]);
        }
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === '406007') {
            form.setFields([
              {
                name: 'addressLine1',
                errors: [t('orderEntryForms.address.addressOutOfServiceArea')],
              },
            ]);
          }
        }
      }
    }
  };
  const onAutocompleteChangeHandler = () => {
    const val = form.getFieldsValue(['province', 'city', 'country', 'postalCode']);
    if (val.province || val.city || val.country || val.postalCode) {
      form.resetFields(['province', 'city', 'country', 'postalCode']);
    }
  };
  const InitialValue = useMemo(
    () => (selectedPickupAddress ? form.getFieldsValue(true) : {}),
    [selectedPickupAddress]
  );

  return (
    <Form
      form={form}
      layout="vertical"
      onFieldsChange={(changesFields) => {
        if (changesFields.length <= 1) {
          const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true));
          setIsFieldChanged({ ...isFieldChanged, pickUpAddress: isIsChange });
        }
      }}
      className="pickup-address-form"
    >
      <Row gutter={16}>
        {getFieldConfig(fieldConfig, 'name').isVisible && (
          <Col xs={24} md={12}>
            <Form.Item
              label={t('customerAddressPage.operationalForm.name')}
              name="name"
              validateFirst
              rules={[
                ...createValidationRules(
                  getFieldConfig(fieldConfig, 'name'),
                  t('customerAddressPage.operationalForm.nameError')
                ),
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
                {
                  pattern: formErrorRegex.NoSpecialCharacters,
                  message: t('common.errors.noSpacialCharacters'),
                },
                {
                  whitespace: true,
                  message: t('customerAddressPage.operationalForm.nameError'),
                },
              ]}
              className="address-form-item"
            >
              <Input
                className="address-select-item"
                placeholder={t('customerAddressPage.operationalForm.namePlaceholder')}
                maxLength={50}
              />
            </Form.Item>
          </Col>
        )}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>

        {getFieldConfig(fieldConfig, 'companyName').isVisible && (
          <Col xs={24} md={12}>
            <Form.Item
              label={t('dashboard.customer.columns.formFieldNames.companyName')}
              name="companyName"
              rules={createValidationRules(
                getFieldConfig(fieldConfig, 'companyName'),
                t('orderEntryForms.address.companyNameRequired')
              )}
              className="address-form-item"
            >
              <Input
                className="address-select-item"
                placeholder={t('orderEntryForms.address.searchOrInputContactName')}
              />
            </Form.Item>
          </Col>
        )}
      </Row>

      {getFieldConfig(fieldConfig, 'addressLine1').isVisible && (
        <Row gutter={16}>
          <Col span={24}>
            <CustomGoogleAutoComplete
              onLoad={onLoad}
              onPlaceChanged={onPlaceChanged}
              ref={autocompleteRef}
            >
              <Form.Item
                validateFirst
                label={t('customerAddressPage.operationalForm.addressLine1')}
                rules={[
                  ...createValidationRules(
                    getFieldConfig(fieldConfig, 'addressLine1'),
                    t('customerAddressPage.operationalForm.addressLine1Error')
                  ),
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                  {
                    whitespace: true,
                    message: t('customerAddressPage.operationalForm.addressLine1Error'),
                  },
                ]}
                name="addressLine1"
                className="address-form-item "
              >
                <Input
                  className="h-[40px]"
                  placeholder={t('customerAddressPage.operationalForm.addressLine1Placeholder')}
                  maxLength={255}
                  id="autoComplete"
                  onChange={onAutocompleteChangeHandler}
                />
              </Form.Item>
            </CustomGoogleAutoComplete>
          </Col>
        </Row>
      )}

      {getFieldConfig(fieldConfig, 'addressLine2').isVisible && (
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              className="address-form-item"
              label={t('dashboard.customer.columns.formFieldNames.addressLine2')}
              name="addressLine2"
              rules={createValidationRules(
                getFieldConfig(fieldConfig, 'addressLine2'),
                'Address Line 2 is required'
              )}
            >
              <Input 
                className="address-select-item" 
                placeholder="123 Main St" 
              />
            </Form.Item>
          </Col>
        </Row>
      )}

      <Row gutter={16}>
        {getFieldConfig(fieldConfig, 'city').isVisible && (
          <Col xs={24} md={12}>
            <Form.Item
              className="address-form-item"
              label={t('addressPage.operationalForm.city')}
              name="city"
              rules={createValidationRules(
                getFieldConfig(fieldConfig, 'city'),
                t('customerAddressPage.operationalForm.cityError')
              )}
            >
              <Input
                disabled
                placeholder={t('customerAddressPage.operationalForm.cityPlaceholder')}
                className="address-select-item"
              />
            </Form.Item>
          </Col>
        )}

        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.province')}
            name="province"
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.provinceError') },
            ]}
          >
            <Input
              disabled
              placeholder={t('customerAddressPage.operationalForm.provincePlaceholder')}
              className="address-select-item"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="address-form-item"
            label={t('addressPage.operationalForm.country')}
            name="country"
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.countryError') },
            ]}
          >
            <Input disabled placeholder="Canada" className="address-select-item" />
          </Form.Item>
        </Col>

        {getFieldConfig(fieldConfig, 'postalCode').isVisible && (
          <Col xs={24} md={12}>
            <Form.Item
              className="address-form-item"
              label={t('addressPage.operationalForm.postalCode')}
              name="postalCode"
              rules={createValidationRules(
                getFieldConfig(fieldConfig, 'postalCode'),
                t('customerAddressPage.operationalForm.postalCodeError')
              )}
            >
              <Input 
                disabled 
                placeholder="H3Z 2Y7" 
                className="address-select-item" 
              />
            </Form.Item>
          </Col>
        )}
      </Row>

      <Row gutter={16}>
        {getFieldConfig(fieldConfig, 'email').isVisible && (
          <Col xs={24} md={12}>
            <Form.Item
              className="address-form-item"
              label={t('addressPage.operationalForm.email')}
              name="email"
              rules={[
                ...createValidationRules(
                  getFieldConfig(fieldConfig, 'email'),
                  t('customerAddressPage.operationalForm.emailError')
                ),
                {
                  pattern: formErrorRegex.ValidEmailOrNot,
                  message: t('customerAddressPage.operationalForm.emailTypeError'),
                },
              ]}
            >
              <Input 
                placeholder="<EMAIL>" 
                className="address-select-item" 
              />
            </Form.Item>
          </Col>
        )}

        {getFieldConfig(fieldConfig, 'phone').isVisible && (
          <Col xs={24} md={12}>
            <Space.Compact className="combined-masked-input address-form-maskedItem w-full">
              <Form.Item
                className="address-form-maskedItem w-full"
                dependencies={['phoneCountryCode']}
                validateFirst
                rules={[
                  ...createValidationRules(
                    getFieldConfig(fieldConfig, 'phone'),
                    t('customerAddressPage.operationalForm.phoneNumberError')
                  ),
                  {
                    validator: validateCountryAndValue(
                      form,
                      'phoneCountryCode',
                      'phone number',
                      getFieldConfig(fieldConfig, 'phone').isRequired
                    ),
                  },
                  {
                    validator: (_, value) =>
                      validateMaskedInput(
                        value,
                        maskPhoneInput.length,
                        t('customerAddressPage.operationalForm.validPhoneNumberError')
                      ),
                  },
                ]}
                name="phone"
                label={t('customerAddressPage.operationalForm.phoneNumber')}
              >
                <MaskedInput
                  ref={inputPhoneRef}
                  addonBefore={phoneNumberPrefix}
                  className="address-form-maskedItem"
                  placeholder={t('customerAddressPage.operationalForm.phoneNumberPlaceholder')}
                  mask={maskPhoneInput.mask}
                  onChange={(event) => form.setFieldValue('phone', event?.unmaskedValue)}
                />
              </Form.Item>
              <Form.Item name="phoneExtension" className="w-[25%] !mb-0">
                <Input
                  onKeyDown={(e) => numberFieldValidator(e, {})}
                  placeholder="00000"
                  maxLength={6}
                  className="address-select-item mt-[30px]"
                />
              </Form.Item>
            </Space.Compact>
          </Col>
        )}
      </Row>

      {getFieldConfig(fieldConfig, 'notes').isVisible && (
        <Row gutter={16}>
          {/* Notes */}
          <Col span={24}>
            <Form.Item 
              label={t('customerAddressPage.operationalForm.comments')} 
              name="notes"
              rules={createValidationRules(
                getFieldConfig(fieldConfig, 'notes'),
                'Comments are required'
              )}
            >
              <TextArea
                placeholder={t('customerAddressPage.operationalForm.commentsPlaceHolder')}
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
          </Col>
        </Row>
      )}
    </Form>
  );
};

export default PickupAddressForm;
