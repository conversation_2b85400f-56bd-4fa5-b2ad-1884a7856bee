import React, { useMemo } from 'react';
import { Radio, Typography, Collapse, Skeleton } from 'antd';
import { IGetAvailableServices } from '@/api/service/service.types';
import dayjs from 'dayjs';
import { CollapseDownIcon, CollapseUpIcon } from '@/assets';
import { getUserInfoFromStorage } from '@/lib/helper/userHelper';
import { TimeFormats } from '@/api/tenantSettings/tenantSettings.types';
import { DEV_CONFIG } from '@/config/devConfig';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
const { Text } = Typography;
const { Panel } = Collapse;

interface AvailableDeliveryServicesProps {
  value?: string;
  availableServices: IGetAvailableServices[];
  setSelectedDeliveryService: (service: IGetAvailableServices) => void;
  getServicesStatus: string;
  isAbleToEdit: boolean;
}

const AvailableDeliveryServices: React.FC<AvailableDeliveryServicesProps> = ({
  availableServices,
  value,
  setSelectedDeliveryService,
  getServicesStatus,
  isAbleToEdit,
}) => {
  const currentUser = getUserInfoFromStorage();
  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  const dateTimeFormate = useMemo(
    () =>
      tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
        ? DEV_CONFIG.DATE_FORMATE_WITH_TIME_12H
        : DEV_CONFIG.DATE_FORMATE_WITH_TIME_24H,
    [tenantSettings?.globalConfiguration?.timeFormat]
  );

  const timeFormate = useMemo(
    () =>
      tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
        ? DEV_CONFIG.TIME_FORMATE_12H
        : DEV_CONFIG.TIME_FORMATE_24H,
    [tenantSettings?.globalConfiguration?.timeFormat]
  );

  // const expectedDeliveryFormat = (deliveryDate: string) => {
  //   const diff = dayjs(deliveryDate).startOf('day').diff(dayjs().startOf('day'), 'day');

  //   switch (diff) {
  //     case 0:
  //       return `Expected delivery today by ${dayjs(deliveryDate).format(timeFormate)}.`;
  //     case 1:
  //       return `Expected delivery by tomorrow ${dayjs(deliveryDate).format(timeFormate)}.`;
  //     default:
  //       return `Expected delivery by ${dayjs(deliveryDate).format(dateTimeFormate)}.`;
  //   }
  // };

  dayjs.extend(utc);
dayjs.extend(timezone);


const expectedDeliveryFormat = (deliveryDate: string, timezone: string) => {
  const deliveryMoment = dayjs(deliveryDate);
  const nowInTenantTz = dayjs().tz(timezone);

  const deliveryDayStart = deliveryMoment.startOf('day');
  const nowDayStart = nowInTenantTz.startOf('day');
  const diff = deliveryDayStart.diff(nowDayStart, 'day');

  switch (diff) {
    case 0:
      return `Expected delivery today by ${deliveryMoment.format(timeFormate)}.`;
    case 1:
      return `Expected delivery by tomorrow ${deliveryMoment.format(timeFormate)}.`;
    default:
      return `Expected delivery by ${deliveryMoment.format(dateTimeFormate)}.`;
  }
};
  return (
    <Collapse
      activeKey={availableServices.length > 0 ? '1' : []}
      expandIconPosition="end"
      expandIcon={({ isActive }) => {
        return (
          <span className="text-[#2D3484] font-semibold text-[14px]">
            {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
          </span>
        );
      }}
      className="form-collapse services-collapse"
    >
      <Panel
        header={
          <div className="flex flex-col">
            <span className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb]`}>
              Available delivery services
            </span>
          </div>
        }
        key="1"
      >
        <Radio.Group
          onChange={(e) => {
            const selectedService = availableServices.find(
              (service) => service.id === e.target.value
            );
            if (selectedService) {
              setSelectedDeliveryService(selectedService);
            }
          }}
          value={value}
          style={{ width: '100%' }}
        >
          {getServicesStatus === 'pending' ? (
            <div className="flex flex-col gap-2 p-4 border rounded-lg">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="flex items-center gap-3 p-3 border rounded">
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: 16, height: 16, minWidth: 16 }}
                  />
                  <div className="flex-1">
                    <Skeleton.Input
                      active
                      size="small"
                      style={{ width: '100%', marginBottom: 4 }}
                    />
                    <Skeleton.Input active size="small" style={{ width: '60%' }} />
                  </div>
                  <Skeleton.Input active size="small" style={{ width: 80 }} />
                </div>
              ))}
            </div>
          ) : !isAbleToEdit ? (
            (() => {
              const selectedService = availableServices.find((service) => service.id === value);
              return (
                selectedService && (
                  <div className={'delivery-service-item'}>
                    <Radio value={selectedService.id} className="delivery-service-radio">
                      <div className="delivery-service-content w-full">
                        <div className="delivery-service-row">
                          <Text className="delivery-service-name">{selectedService.name}</Text>
                          {selectedService?.pricing && (
                            <Text className="delivery-service-price">
                              {currentUser?.permissions?.prices
                                ? `${selectedService.pricing.requiredTotalPrice?.toFixed(2)}`
                                : '***'}
                            </Text>
                          )}
                        </div>
                        <Text className="delivery-service-time">
                          {expectedDeliveryFormat(selectedService.deliveryDate , selectedService.timezone)}
                        </Text>{' '}
                      </div>
                    </Radio>
                  </div>
                )
              );
            })()
          ) : (
            <div className="delivery-services-list">
              {availableServices.map((service, index) => (
                <div
                  key={service.id}
                  className={`delivery-service-item ${index < availableServices.length - 1 ? 'with-divider' : ''}`}
                >
                  <Radio value={service.id} className="delivery-service-radio">
                    <div className="delivery-service-content w-full">
                      <div className="delivery-service-row">
                        <Text className="delivery-service-name">{service.name}</Text>
                        {service?.pricing ? (
                          <Text className="delivery-service-price">
                            {currentUser?.permissions?.prices
                              ? `$${service.pricing.requiredTotalPrice?.toFixed(2)}`
                              : '***'}
                          </Text>
                        ) : (
                          ''
                        )}
                      </div>
                      <Text className="delivery-service-time">
                        {expectedDeliveryFormat(service.deliveryDate , service.timezone)}
                      </Text>{' '}
                    </div>
                  </Radio>
                </div>
              ))}
            </div>
          )}
        </Radio.Group>
      </Panel>
    </Collapse>
  );
};

export default AvailableDeliveryServices;
