import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { useLanguage } from '@/hooks/useLanguage';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ICellRendererParams, IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { blobToUrlNavigation, getPaginationData } from '@/lib/helper';
import ColumnManage from '@/components/specific/columnManage';
import Icon, { CloseOutlined, LoadingOutlined } from '@ant-design/icons';
import { DeleteIcon, EditPopupIcon, EyeIcon, PrinterIcon } from '@/assets';
import { Divider } from 'antd';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { defaultPagination } from '@/constant/generalConstant';
import { IContextMenuItems, onContextMenuItemClickParams } from '@/types/ContextMenuTypes';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import QuickFilter from '@/components/specific/quickFilter/QuickFilter';
import { orderService, ordersServiceHook } from '@/api/order/useOrders';
import { IOrder, IUpdateOrderStatusPayload } from '@/api/order/order.types';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { SendTrackingLinkIcon } from '@/assets/icons/sendTrackingLinkIcon';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { AdvanceFilterColDefs } from '@/components/specific/searchFilter/searchFilter.types';
import { OrderBills, OrderStatusEnums } from '@/types/enums/order';
import SendTrackingLinkModal from '@/components/common/sendTrackingLinkModal/SendTrackingLinkModal';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import { DEV_CONFIG } from '@/config/devConfig';
import { TimeFormats } from '@/api/tenantSettings/tenantSettings.types';
import { CellContextMenuEvent } from 'ag-grid-community';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useNotificationManager } from '@/hooks/useNotificationManger';

const OrderListPage = () => {
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [loadingBillType, setLoadingBillType] = useState<OrderBills | null>(null);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const { t } = useLanguage();

  const {
    data: order,
    isLoading,
    isFetching,
    refetch: refetchOrders,
  } = ordersServiceHook.useList(filterParams, { staleTime: 3000, retry: 1 });
  const updateStatusMutation = ordersServiceHook.usePatch<IUpdateOrderStatusPayload>();
  const notificationManager = useNotificationManager();

  const [orders, setOrders] = useState<IOrder[]>();
  const gridRef = useRef<AgGridReact<IOrder>>(null);
  const [isSendTrackingLinkModelOpen, setIsSendTrackingLinkModelOpen] = useState({
    isOpen: false,
    order: null,
  });
  const navigate = useNavigate();

  useEffect(() => {
    if (order) {
      setOrders(order.data);
    }
  }, [order]);

  const paginationData = useMemo(() => getPaginationData(order), [order]);

  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  const { data: OrderManagementSettingResponse } = tenantSettingsHook.useEntity(
    'OrderManagementSetting',
    {
      staleTime: 60000,
    }
  );

  const orderBillPDFViewHandler = async (id: string, billType: OrderBills) => {
    try {
      setLoadingBillType(billType);
      const response = await orderService.getById<Blob>(`${id}/bills/${billType}`, undefined, {
        responseType: 'blob',
      });
      blobToUrlNavigation(response);
    } catch (error) {
      // handle error (handled by error code)
    } finally {
      setLoadingBillType(null);
    }
  };

  const isOrderCancellationAllowed = useCallback(
    (params: CellContextMenuEvent<any, any>) => {
      const status = params?.data?.status;
      const settings = OrderManagementSettingResponse?.orderManagementSetting;

      // Always disabled for these statuses
      const alwaysDisabledStatuses = [
        OrderStatusEnums.DRAFT,
        OrderStatusEnums.COMPLETED,
        OrderStatusEnums.CANCELLED,
      ];

      if (alwaysDisabledStatuses.includes(status)) {
        return true;
      }

      if (status === OrderStatusEnums.SUBMITTED) {
        return !settings?.allowCancellationWhenSubmitted;
      }

      if (status === OrderStatusEnums.IN_TRANSIT) {
        return !settings?.allowCancellationDuringTransit;
      }

      return false;
    },
    [OrderManagementSettingResponse?.orderManagementSetting]
  );

  const orderCancellationMessage = useCallback(
    (params: onContextMenuItemClickParams) => {
      const status = params?.rowData?.status;
      const defaultMessage = t('ordersPage.cancelOrderDefaultMessage');
      if (status === OrderStatusEnums.SUBMITTED) {
        return (
          OrderManagementSettingResponse?.orderManagementSetting?.submittedCancellationMessage ||
          defaultMessage
        );
      }
      if (status === OrderStatusEnums.IN_TRANSIT) {
        return (
          OrderManagementSettingResponse?.orderManagementSetting?.transitOrderCancellationMessage ||
          defaultMessage
        );
      }
      return defaultMessage;
    },
    [
      OrderManagementSettingResponse?.orderManagementSetting?.submittedCancellationMessage,
      OrderManagementSettingResponse?.orderManagementSetting?.transitOrderCancellationMessage,
      t,
    ]
  );

  const cancelOrderHandler = useCallback(
    async (params: onContextMenuItemClickParams) => {
      customAlert.error({
        title: t('ordersPage.cancelOrderTitle'),
        message: orderCancellationMessage(params),

        firstButtonTitle: t('ordersPage.yesCancelButton'),
        secondButtonTitle: t('ordersPage.noCancelButton'),
        firstButtonFunction: async () => {
          try {
            await updateStatusMutation.mutateAsync({
              id: `${params.rowData.id}/cancelOrder`,
              data: {
                status: OrderStatusEnums.CANCELLEDBILLABLE,
                reason: 'Cancelled by user',
                comments: 'Cancelled by user',
              },
            });
            await refetchOrders();
            notificationManager.success({
              message: t('common.success'),
              description: 'Order cancelled successfully',
            });
            customAlert.destroy();
          } catch {
            // handled by error code
          }
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
    },
    [t, orderCancellationMessage, updateStatusMutation, refetchOrders, notificationManager]
  );

  const ordersContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('ordersPage.contextMenu.view'),
        key: 'viewOrder',
        onClick: (params: onContextMenuItemClickParams) =>
          navigate(`${ROUTES.ORDER.LISTING}/${params.rowData.id}`),
        icon: EyeIcon,
        disabled: (params) => params?.data?.status === OrderStatusEnums.DRAFT,
      },
      {
        label: t('ordersPage.contextMenu.edit'),
        key: 'EditOrder',
        icon: EditPopupIcon,
        onClick: async ({ rowData }) => {
          navigate(ROUTES.ORDER.ORDER_EDIT.replace(':id', rowData.id as string));
        },
        disabled: (params) => params?.data?.status === OrderStatusEnums.COMPLETED,
      },
      // TODO: Add approve order functionality in V2
      // {
      //   label: t('ordersPage.contextMenu.approveOrder'),
      //   icon: ApproveOrderIcon,
      //   key: 'unAssign',
      //   onClick: async (params: onContextMenuItemClickParams) => {
      //     params.closeContextMenu();
      //   },
      // },
      {
        label: t('ordersPage.contextMenu.sendTrackingLink'),
        icon: SendTrackingLinkIcon,
        key: 'sendTrackingLink',
        disabled: (params) => params?.data?.status === OrderStatusEnums.DRAFT,
        onClick: ({ rowData }) => setIsSendTrackingLinkModelOpen({ isOpen: true, order: rowData }),
      },
      {
        label: t('ordersPage.contextMenu.printLabel'),
        key: 'PrintLabel',
        icon: PrinterIcon,
        disabled: (params) => params?.data?.status === OrderStatusEnums.DRAFT,
        subMenu: [
          {
            label: t('ordersPage.contextMenu.printOptions.shippingBill'),
            key: 'shippingBill',
            icon: loadingBillType === OrderBills.SHIPPING_LABEL ? LoadingOutlined : '',
            onClick: ({ rowData }) =>
              rowData.id && orderBillPDFViewHandler(rowData.id, OrderBills.SHIPPING_LABEL),
          },
          {
            label: t('ordersPage.contextMenu.printOptions.wayBill'),
            key: 'wayBill',
            icon: loadingBillType === OrderBills.WAY_BILL ? LoadingOutlined : '',
            onClick: ({ rowData }) =>
              rowData.id && orderBillPDFViewHandler(rowData.id, OrderBills.WAY_BILL),
          },
          {
            label: t('ordersPage.contextMenu.printOptions.billOfLanding'),
            key: 'BillOfLanding',
            icon: loadingBillType === OrderBills.BILL_OF_LADING ? LoadingOutlined : '',
            onClick: ({ rowData }) =>
              rowData.id && orderBillPDFViewHandler(rowData.id, OrderBills.BILL_OF_LADING),
          },
        ],
      },
      // TODO: Add approve order functionality in V2
      // {
      //   label: t('ordersPage.sendNotification'),
      //   icon: NotificationBellIcon,
      //   key: 'SendNotification',
      //   subMenu: [
      //     {
      //       label: t('ordersPage.sendStatusToCustomer'),
      //       key: 'SendStatusToCustomer',
      //       subMenu: [
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.sms'),
      //           key: 'CustomerSms',
      //         },
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.email'),
      //           key: 'CustomerEmail',
      //         },
      //       ],
      //     },
      //     {
      //       label: t('ordersPage.sendStatusToReceiver'),
      //       key: 'SendStatusToReceiver',
      //       subMenu: [
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.sms'),
      //           key: 'CustomerSms',
      //         },
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.email'),
      //           key: 'CustomerEmail',
      //         },
      //       ],
      //     },
      //     {
      //       label: t('ordersPage.sendStatusToCollector'),
      //       key: 'SendStatusToCollector',
      //       subMenu: [
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.sms'),
      //           key: 'CustomerSms',
      //         },
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.email'),
      //           key: 'CustomerEmail',
      //         },
      //       ],
      //     },
      //     {
      //       label: t('ordersPage.sendStatusToDriver'),
      //       key: 'SendStatusToDriver',
      //       subMenu: [
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.sms'),
      //           key: 'CustomerSms',
      //         },
      //         {
      //           label: t('dashboard.customer.settings.settingsNotification.email'),
      //           key: 'CustomerEmail',
      //         },
      //       ],
      //     },
      //   ],
      // },
      {
        label: 'Cancel',
        key: 'cancel',
        icon: CloseOutlined,
        disabled: isOrderCancellationAllowed,
        onClick: cancelOrderHandler,
      },
      {
        label: <span className="text-red-500">{t('common.delete')}</span>,
        icon: DeleteIcon,
        key: 'delete',
      },
    ];
  }, [cancelOrderHandler, isOrderCancellationAllowed, loadingBillType, navigate, t]);

  const viewOrder = useCallback(
    (params: ICellRendererParams<IOrder>) => {
      navigate(`${ROUTES.ORDER.LISTING}/${params.data.id}`);
    },
    [navigate]
  );

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.order.sortable.includes(field);
  }, []);

  const dateTimeFormate = useMemo(
    () =>
      tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
        ? DEV_CONFIG.DATE_TIME_SECOND_FORMATE_12H
        : DEV_CONFIG.DATE_TIME_SECOND_FORMATE_24H,
    [tenantSettings?.globalConfiguration?.timeFormat]
  );

  const orderColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'trackingNumber',
        headerName: t('ordersPage.trackingNumber'),
        sortable: isColumnSortable('trackingNumber'),
        unSortIcon: isColumnSortable('trackingNumber'),
        tooltipField: 'trackingNumber',
        minWidth: 200,
        flex: 1,
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'status',
        headerName: t('ordersPage.status'),
        sortable: isColumnSortable('status'),
        unSortIcon: isColumnSortable('status'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'dropdown',
        cellRenderer: (params: { value: string }) => {
          switch (params.value) {
            case 'Draft':
              return (
                <span className="grey-chip block w-full">{t('ordersPage.statusValues.draft')}</span>
              );
            case 'Submitted':
              return (
                <span className="primary-chip block w-full">
                  {t('ordersPage.statusValues.submitted')}
                </span>
              );
            case 'Cancelled':
              return (
                <span className="error-chip block w-full">
                  {t('ordersPage.statusValues.cancelled')}
                </span>
              );
            case 'InTransit':
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.inTransit')}
                </span>
              );
            case 'Pending':
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.pending')}
                </span>
              );
            case 'Assigned':
              return (
                <span className="primary-chip block w-full">
                  {t('ordersPage.statusValues.assigned')}
                </span>
              );
            case 'Completed':
              return (
                <span className="success-chip block w-full">
                  {t('ordersPage.statusValues.completed')}
                </span>
              );
             case 'CancelledBillable':
              return (
                <span className="error-chip block w-full">
                  {t('ordersPage.statusValues.cancelledBillable')}
                </span>
              );  
            default:
              break;
          }
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'collectionCompanyName',
        headerName: t('ordersPage.collectionComponyName'),
        sortable: isColumnSortable('collectionCompanyName'),
        unSortIcon: isColumnSortable('collectionCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'deliveryCompanyName',
        headerName: t('ordersPage.deliveryComponyName'),
        sortable: isColumnSortable('deliveryCompanyName'),
        unSortIcon: isColumnSortable('deliveryCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'createdAt',
        type: 'date',
        headerName: t('ordersPage.dateSubmitted'),
        minWidth: 200,
        flex: 1,
        sortable: isColumnSortable('createdAt'),
        unSortIcon: isColumnSortable('createdAt'),
        visible: true,
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value, dateTimeFormate) || '' : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'customerName',
        headerName: t('ordersPage.customerName'),
        sortable: isColumnSortable('customerName'),
        unSortIcon: isColumnSortable('customerName'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'scheduledCollectionTime',
        headerName: t('ordersPage.collectionTime'),
        sortable: isColumnSortable('scheduledCollectionTime'),
        unSortIcon: isColumnSortable('scheduledCollectionTime'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'date',
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value, dateTimeFormate) || '' : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'scheduledDeliveryTime',
        headerName: t('ordersPage.deliveryTime'),
        sortable: isColumnSortable('scheduledDeliveryTime'),
        unSortIcon: isColumnSortable('scheduledDeliveryTime'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'date',
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? dateFormatter(params.value, dateTimeFormate) || '' : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'serviceLevel',
        headerName: t('ordersPage.serviceLevel'),
        sortable: isColumnSortable('serviceLevel'),
        unSortIcon: isColumnSortable('serviceLevel'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'assignedDriverName',
        headerName: t('ordersPage.assignee'),
        sortable: isColumnSortable('assignedDriver'),
        unSortIcon: isColumnSortable('assignedDriver'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return params.value ? (
            searchText ? (
              highlightText(params.value, searchText)
            ) : (
              params.value
            )
          ) : (
            <span className="error-chip">{'Unassigned'}</span>
          );
        },
      },
      {
        field: 'action',
        headerName: t('ordersPage.action'),
        pinned: 'right',
        width: 70,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<IOrder>) => {
          return (
            <div className="flex gap-2 h-full items-center justify-start w-full overflow-hidden">
              {params.data.status != 'Draft' && (
                <Icon
                  component={() => <EyeIcon bool={false} />}
                  onClick={() => {
                    viewOrder(params);
                  }}
                  className="cursor-pointer"
                />
              )}
              {params.data.status !== 'Completed' && (
                <Icon
                  component={() => <EditPopupIcon bool={false} />}
                  onClick={() => {
                    navigate(ROUTES.ORDER.ORDER_EDIT.replace(':id', params.data.id as string));
                  }}
                  className="cursor-pointer"
                />
              )}
            </div>
          );
        },
        visible: true,
      },
    ];
  }, [t, isColumnSortable, searchText, dateTimeFormate, viewOrder, navigate]);

  const advanceFilterColdDefs: AdvanceFilterColDefs[] = useMemo(() => {
    return orderColDefs.map((col) => {
      if (col.field === 'status') {
        return {
          ...col,
          dropDownOptions: [
            { value: 'Draft', label: 'Draft' },
            { value: 'Submitted', label: 'Submitted' },
            { value: 'Cancelled', label: 'Cancelled' },
            { value: 'GoingForPickup,GoingForDelivery,PickedUp', label: 'In Transit' },
            { value: 'Pending', label: 'Pending' },
            { value: 'Assigned', label: 'Assigned' },
            { value: 'Completed', label: 'Completed' },
          ],
        };
      }
      return col;
    }) as AdvanceFilterColDefs[];
  }, [orderColDefs]);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.customerPortalOrderGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const maskedAppliedFilters = data.filters.map((filter) => {
        if (filter.value === 'InTransit') {
          filter.value = `GoingForPickup,GoingForDelivery,PickedUp`;
        }
        return filter;
      }) as IAssignedFilters[];
      const filterObject = await advanceFilterObjectMapper(maskedAppliedFilters, {
        defaultOperator: 'in',
      });

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0
          ? (maskQuickFilterData(data.filters, {
              'GoingForPickup,GoingForDelivery,PickedUp': 'InTransit',
            }) as IAssignedFilters[])
          : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  const closeModel = (isOpen: boolean) => {
    setIsSendTrackingLinkModelOpen({ isOpen, order: null });
  };

  return (
    <>
      <SendTrackingLinkModal
        isOpen={isSendTrackingLinkModelOpen?.isOpen}
        setIsOpen={closeModel}
        order={isSendTrackingLinkModelOpen?.order}
      />
      <div className={`h-[93%] px-5`}>
        <header className="flex justify-between items-center gap-3">
          <div className=" text-[#090A1A] font-semibold text-3xl self-end">
            {t('ordersPage.orderList')}
          </div>
          <div className="flex justify-end items-center gap-3">
            <SearchFilterComponent
              colDefs={advanceFilterColdDefs}
              isSetQuickFilter
              searchInputPlaceholder={t('ordersPage.searchOrder')}
              onSearch={searchHandler}
              setQuickFilters={() => {}}
              onFilterApply={applyFilters}
              setSelectedQuickFilterData={setSelectedQuickFilterData}
              supportedFields={filterableModules.order.advanceFilter}
              clearAllFunctionRef={clearAllFunctionRef}
              setFilterParams={setFilterParams}
              quickFilterEventKey={'CustomerPortalOrderQuickFilter'}
              quickFilterSettingsKey={'CustomerPortalOrderQuickFilter'}
              quickFilterTitleEventKey={'CustomerPortalOrderQuickFilterTitleEvent'}
              gridName={GridNames.customerPortalOrderGrid}
            />
            <ColumnManage colDefs={orderColDefs} gridName={GridNames.customerPortalOrderGrid} />
            <div className="pt-5">
              <Divider type="vertical" className="h-[40px] !m-0" />
            </div>
            <div className="flex gap-2 pt-5">
              <QuickFilter
                eventKey={'CustomerPortalOrderQuickFilter'}
                quickFilterTitleEventKey={'CustomerPortalOrderQuickFilterTitleEvent'}
                clearAllToDefault={clearAllToDefault}
                setFilterParams={setFilterParams}
              />
            </div>
          </div>
        </header>
        <main
          className={`${selectedQuickFilterData.length > 0 ? '!h-full overflow-y-hidden' : 'h-full'} overflow-x-hidden overflow-y-auto bg-white`}
        >
          <ActiveFilters
            selectedQuickFilterData={selectedQuickFilterData}
            clearAllToDefault={clearAllToDefault}
            colDefs={orderColDefs}
            className={'pt-5'}
          />
          <div className="mx-auto h-full flex justify-center items-center pt-3">
            <CustomAgGrid
              rowData={orders}
              gridRef={gridRef}
              columnDefs={orderColDefs}
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
              onCellClicked={(params) => {
                if (
                  params.colDef.field !== 'action' &&
                  params.data.status !== OrderStatusEnums.DRAFT
                ) {
                  navigate(`${ROUTES.ORDER.LISTING}/${params.data.id}`);
                }
              }}
              loading={isFetching || isLoading}
              isContextMenu
              contextMenuItem={ordersContextMenuItems}
              className={`3xsm:!h-[62vh] md:!h-[72vh] ${selectedQuickFilterData.length > 0 ? 'lg:!h-[69vh]' : 'lg:!h-[74vh]'}`}
              gridName={GridNames.customerPortalOrderGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('ordersPage.emptyState.title'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('ordersPage.emptyState.description'),
              }}
            />
          </div>
        </main>
      </div>
    </>
  );
};

export default OrderListPage;
