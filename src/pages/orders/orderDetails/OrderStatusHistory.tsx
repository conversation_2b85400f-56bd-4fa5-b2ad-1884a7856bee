
import { Steps } from 'antd';
import React, { useMemo } from 'react';
import dayjs from 'dayjs';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import { TimeFormats } from '@/api/tenantSettings/tenantSettings.types';
import { DEV_CONFIG } from '@/config/devConfig';

export enum OrderStatus {
  Draft = 0,
  Submitted = 1,
  Assigned = 2,
  InTransit = 3,
  Completed = 4,
  Cancelled = 5,
  CancelledBillable = 6,
}

const statusMeta: Record<
  keyof typeof OrderStatus,
  {
    title: string;
    defaultDescription: string;
    pendingDescription: string;
  }
> = {
  Draft: {
    title: 'Draft',
    defaultDescription: 'Order is being created.',
    pendingDescription: 'Order creation pending.',
  },
  Submitted: {
    title: 'Submitted',
    defaultDescription: 'Order submitted.',
    pendingDescription: 'Awaiting order submission.',
  },
  Assigned: {
    title: 'Assigned',
    defaultDescription: 'Driver assigned.',
    pendingDescription: 'Waiting for driver assignment.',
  },
  InTransit: {
    title: 'In transit',
    defaultDescription: 'Out for delivery.',
    pendingDescription: 'Awaiting dispatch for delivery.',
  },
  Completed: {
    title: 'Completed',
    defaultDescription: 'Delivered successfully.',
    pendingDescription: 'Delivery pending.',
  },
  Cancelled: {
    title: 'Cancelled',
    defaultDescription: 'Order has been cancelled.',
    pendingDescription: 'Awaiting cancellation confirmation.',
  },
  CancelledBillable: {
    title: 'Cancelled (Billable)',
    defaultDescription: 'Order cancelled with billable charges.',
    pendingDescription: 'Awaiting billing cancellation confirmation.',
  },
};

interface StatusHistoryItem {
  newStatus: keyof typeof OrderStatus;
  changedAt: string;
  comments?: string | null;
}

interface Props {
  orderDetails: {
    statusHistory?: StatusHistoryItem[];
  };
}

const OrderStatusSteps: React.FC<Props> = ({ orderDetails }) => {
  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  const stepsData = useMemo(() => {
    const history = orderDetails?.statusHistory || [];

    const latestStatusMap = history.reduce(
      (acc, item) => {
        const existing = acc[item.newStatus];
        if (!existing || dayjs(item.changedAt).isAfter(dayjs(existing.changedAt))) {
          acc[item.newStatus] = item;
        }
        return acc;
      },
      {} as Record<string, StatusHistoryItem>
    );

    const cancelStatus = latestStatusMap['Cancelled']
      ? 'Cancelled'
      : latestStatusMap['CancelledBillable']
        ? 'CancelledBillable'
        : null;

    const draftPresent = !!latestStatusMap['Draft'];

    const fullNormalFlow: (keyof typeof OrderStatus)[] = draftPresent
      ? ['Draft', 'Submitted', 'Assigned', 'InTransit', 'Completed']
      : ['Submitted', 'Assigned', 'InTransit', 'Completed'];

    let fullStatusOrder: (keyof typeof OrderStatus)[];

    if (cancelStatus) {
      const actualStatuses = Object.keys(latestStatusMap) as (keyof typeof OrderStatus)[];
      const reachedStatuses = fullNormalFlow.filter((s) => actualStatuses.includes(s));
      fullStatusOrder = [...reachedStatuses, cancelStatus];
    } else {
      fullStatusOrder = fullNormalFlow;
    }

    // Determine the current highest status reached
    const reachedStatuses = Object.keys(latestStatusMap) as (keyof typeof OrderStatus)[];
    const highestStatusIndex = Math.max(
      ...reachedStatuses.map((s) => fullStatusOrder.indexOf(s)),
      -1
    );

    const items = fullStatusOrder.map((statusKey, index) => {
      const historyItem = latestStatusMap[statusKey];
      const meta = statusMeta[statusKey];
      const isCancelled = statusKey === 'Cancelled' || statusKey === 'CancelledBillable';

      // ✅ Mark all steps before or equal to highest reached index as done
      const isDone = index <= highestStatusIndex;

      const description =
        historyItem?.comments || (isDone ? meta.defaultDescription : meta.pendingDescription);

      return {
        title: meta.title,
        description: (
          <div className="flex flex-col w-full text-primary-300">
            <span className="text-[12px]">{description}</span>
            {historyItem?.changedAt && (
              <span className="text-[12px]">
                {dayjs(historyItem.changedAt).format(
                  tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
                    ? DEV_CONFIG.DATE_FORMATE_WITH_TIME_12H
                    : DEV_CONFIG.DATE_FORMATE_WITH_TIME_24H
                )}
              </span>
            )}
          </div>
        ),
        isDone,
        isCancelled,
      };
    });

   const currentStepIndex = highestStatusIndex >= 0 ? highestStatusIndex : 0;

return {
  steps: items,
  current: cancelStatus
    ? items.length - 1 // red error at the end for cancelled
    : currentStepIndex, // actual latest reached step (no premature blue)
};
  }, [orderDetails?.statusHistory, tenantSettings?.globalConfiguration?.timeFormat]);

  if (!orderDetails) return null;

  return (
    <div className="border-[1px] border-[#BFC2DF] rounded-[8px] p-2 sm:p-4">
   <Steps
  size="small"
  current={stepsData.current}
  items={stepsData.steps.map(({ title, description, isDone, isCancelled }, index) => {
    const isNext =
      index === stepsData.current + 1 && !isCancelled; // 👈 only the next one gets gray
    return {
      title,
      description,
      className: isNext ? 'upcoming-step' : '',
      status: isCancelled
        ? 'error'
        : isDone
        ? 'finish'
        : index === stepsData.current
        ? 'process'
        : 'wait',
    };
  })}
  className="orders-stepper !border-none"
/>

    </div>
  );
};

export default OrderStatusSteps;
