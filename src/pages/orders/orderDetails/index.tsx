import { orderService, ordersServiceHook } from '@/api/order/useOrders';
import { EditPopupIcon, EmailOutlinedIcon, PhoneOutlinedIcon, PrinterIcon } from '@/assets';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import Icon, { LoadingOutlined } from '@ant-design/icons';
import { Button, Divider, Image, Typography } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import './orderDetails.css';
import { useLanguage } from '@/hooks/useLanguage';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { CustomCollapse } from '@/components/common/customCollaps/CustomCollapse';
import { useMemo, useState } from 'react';
import NotFound404 from '@/components/common/statusFallbackPage/NotFound404';
import { ROUTES } from '@/constant/RoutesConstant';
import OrderStatusSteps from './OrderStatusHistory';
import { IAttachments } from '@/api/order/order.types';
import { fileUploadService } from '@/api/fileUpload/useFileUploads';
import useThrottle from '@/hooks/useThrottle';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { MEME_TYPES } from '@/constant/generalConstant';
import { OrderBills, OrderStatusEnums } from '@/types/enums/order';
import { blobToUrlNavigation, downloadFromBlob } from '@/lib/helper';
import SendTrackingLinkModal from '@/components/common/sendTrackingLinkModal/SendTrackingLinkModal';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import PdfIcon from '@/assets/icons/pdfIcon';
import CsvIcon from '@/assets/icons/csvIcon';
import ExcelIcon from '@/assets/icons/excelIcon';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import { DEV_CONFIG } from '@/config/devConfig';
import { TimeFormats } from '@/api/tenantSettings/tenantSettings.types';

const OrderDetailsPage = () => {
  const { id: OrderId } = useParams();
  const { t } = useLanguage();

  const [isTrackingModelOpen, setIsTrackingModelOpen] = useState(false);
  const [loadingBillType, setLoadingBillType] = useState<OrderBills | null>(null);
  const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);
  const notificationManager = useNotificationManager();

  const { data: orderDetails, error } = ordersServiceHook.useEntity(OrderId as string, {
    enabled: Boolean(OrderId),
    retry: 0,
  });

  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });
  const dateTimeFormate = useMemo(
    () =>
      tenantSettings?.globalConfiguration?.timeFormat === TimeFormats.TWELVE_HOUR
        ? DEV_CONFIG.DATE_FORMATE_WITH_TIME_12H
        : DEV_CONFIG.DATE_FORMATE_WITH_TIME_24H,
    [tenantSettings?.globalConfiguration?.timeFormat]
  );

  const orderSummaryColumn = {
    dataArray: [
      {
        key: 'Level of service',
        label: t('ordersPage.orderDetailsPage.levelOfService'),
        value: orderDetails?.serviceLevel || t('common.notAvailable'),
      },
      {
        key: 'description',
        label: t('ordersPage.orderDetailsPage.description'),
        value: orderDetails?.description || t('common.notAvailable'),
      },
      {
        key: 'weight',
        label: `${t('ordersPage.orderDetailsPage.weightKg')} (${tenantSettings?.globalConfiguration?.weightUnit || 'NA'})`,
        value: orderDetails?.totalWeight || t('common.notAvailable'),
      },
      {
        key: 'totalPrice',
        label: t('ordersPage.orderDetailsPage.totalCost'),
        value: `$${orderDetails?.totalPrice || 0}`,
      },
      {
        key: 'vehicleType',
        label: 'Vehicle type',
        value: orderDetails?.vehicleTypeName || t('common.notAvailable'),
      },
      {
        key: 'codAmount',
        label: 'COD amount',
        value: `$${orderDetails?.codAmount || 0}` || t('common.notAvailable'),
      },
      {
        key: 'notes',
        label: t('ordersPage.orderDetailsPage.notes'),
        value: orderDetails?.internalNotes || t('common.notAvailable'),
      },
    ],
  };

  const trackingDetails = {
    dataArray: [
      {
        key: 'trackingNo',
        label: t('ordersPage.orderInfoDataFields.trackingNo'),
        value: orderDetails?.trackingNumber || t('common.notAvailable'),
      },
      {
        key: 'customer',
        label: t('ordersPage.orderDetailsPage.customer'),
        value: orderDetails?.customerName || t('common.notAvailable'),
      },
      {
        key: 'requestedBy',
        label: t('ordersPage.orderDetailsPage.requestedBy'),
        value: orderDetails?.requestedByName || t('common.notAvailable'),
      },
      {
        key: 'dateSubmitted',
        label: t('ordersPage.orderDetailsPage.dateSubmitted'),
        value: orderDetails?.submittedAt
          ? dateFormatter(orderDetails?.submittedAt as string, dateTimeFormate)
          : t('common.notAvailable'),
      },
      {
        key: 'scheduledCollectionTime',
        label: t('common.scheduledCollectionTime'),
        value: orderDetails?.scheduledCollectionTime
          ? dateFormatter(orderDetails?.scheduledCollectionTime, dateTimeFormate)
          : t('common.notAvailable'),
      },
      {
        key: 'CollectionSignature',
        label: t('ordersPage.orderDetailsPage.collectionSignature'),
        value: orderDetails?.attachments?.pickupSignature ? (
          <div className="border border-primary-100 rounded-lg w-fit p-2">
            <Image
              src={orderDetails?.attachments?.pickupSignature?.url}
              alt=""
              width={72}
              height={72}
              loading="lazy"
            />
          </div>
        ) : (
          t('common.notAvailable')
        ),
      },
      {
        key: 'collectionFrom',
        label: t('ordersPage.orderDetailsPage.collectionFrom'),
        value: orderDetails?.collectionContactName || t('common.notAvailable'),
      },
      {
        key: 'deliveryTo',
        label: t('ordersPage.orderDetailsPage.deliveryTo'),
        value: orderDetails?.deliveryContactName || t('common.notAvailable'),
      },
      {
        key: 'DeliveredBy',
        label: t('ordersPage.orderDetailsPage.deliveredBy'),
        value: orderDetails?.assignedDriverName || t('common.notAvailable'),
      },
      {
        key: 'deliveryOn',
        label: t('ordersPage.orderDetailsPage.deliveredOn'),
        value: orderDetails?.actualDeliveryTime
          ? dateFormatter(orderDetails?.actualDeliveryTime as string, dateTimeFormate)
          : t('common.notAvailable'),
      },

      {
        key: 'dueDate',
        label: t('common.scheduledDeliveryTime'),
        value: orderDetails?.scheduledDeliveryTime
          ? dateFormatter(orderDetails?.scheduledDeliveryTime, dateTimeFormate)
          : t('common.notAvailable'),
      },
      {
        key: 'DeliverySignature',
        label: t('ordersPage.orderDetailsPage.deliverySignature'),
        value: orderDetails?.attachments?.deliverySignature ? (
          <div className="border border-primary-100 rounded-lg w-fit p-2">
            <Image
              src={orderDetails?.attachments?.deliverySignature?.url}
              alt=""
              width={72}
              height={72}
              loading="lazy"
            />
          </div>
        ) : (
          t('common.notAvailable')
        ),
      },
    ],
    images: true,
  };

  const handleDownload = useThrottle(async (attachment: IAttachments | undefined) => {
    try {
      if (attachment && attachment.id) {
        setDownloadingFileId(attachment.id);
        const response = await fileUploadService.getById<any>(
          `download/${attachment.id}`,
          undefined,
          {
            responseType: 'blob',
          }
        );
        downloadFromBlob(response, attachment.originalName);
      }
    } catch (err) {
      notificationManager.error({
        message: t('common.error'),
        description: t('common.failedToDownloadItem'),
      });
    } finally {
      setDownloadingFileId(null);
    }
  }, 3000);

  const renderFileIcon = (memeType: string): any => {
    switch (memeType) {
      case MEME_TYPES.pdf:
        return <PdfIcon />;
      case MEME_TYPES.csv:
        return <CsvIcon />;
      case MEME_TYPES.excel:
        return <ExcelIcon />;
      case MEME_TYPES.excel2:
        return <ExcelIcon />;
      default:
        return <CsvIcon />;
    }
  };

  const collapseDetailsRendered = (obj: any): React.ReactNode => {
    return (
      <>
        <div className="w-full">
          <div className="grid lg:grid-cols-2 w-full gap-2">
            {obj.dataArray?.map((item: { label: string; value: string }, index: number) => {
              return (
                <div className="flex w-full p-1 gap-4">
                  <div key={index} className=" flex items-center lg:w-[25%] w-full">
                    <span className="w-48 flex items-center gap-1 font-medium text-primary-900">
                      {item?.label}:
                    </span>
                  </div>
                  <div className="lg:w-[75%] w-full text-primary-900 font-medium flex items-center">
                    {item?.value}
                  </div>
                </div>
              );
            })}
          </div>
          {obj?.images && (
            <div className="flex justify-between flex-col md:flex-row gap-2">
              <div className="mt-4 flex-1">
                <h2 className="font-semibold text-primary-900 text-base">
                  {t('ordersPage.orderDetailsPage.uploadedImages')}
                </h2>
                <div className="mt-4 flex gap-2">
                  {orderDetails?.attachments?.images?.length === 0
                    ? t('common.notAvailable')
                    : orderDetails?.attachments?.images?.map((img) => (
                        <div className="border border-primary-100 rounded-lg w-fit p-2">
                          <Image src={img?.url} alt="" width={72} height={72} loading="lazy" />
                        </div>
                      ))}
                </div>
              </div>
              <div className="mt-4 flex-1">
                <h2 className="font-semibold text-primary-900 text-base">
                  {t('common.uploadedFiles')}
                </h2>
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {orderDetails?.attachments?.files?.length === 0
                    ? t('common.notAvailable')
                    : orderDetails?.attachments?.files?.map((file) => (
                        <div className="bg-[#F1F4F6] p-3 rounded-lg flex justify-between items-center text-primary-900 font-medium">
                          <div
                            className="flex-1 flex items-center gap-2 min-w-0"
                            title={file.originalName}
                          >
                            <Icon component={() => renderFileIcon(file.mimeType as string)} />
                            <Typography.Text ellipsis className="block">
                              {file.originalName}
                            </Typography.Text>
                          </div>

                          <div className="flex gap-2">
                            {downloadingFileId === file.id ? (
                              <LoadingOutlined />
                            ) : (
                              <Icon
                                title="Download"
                                component={() => <DownloadIcon bool={false} />}
                                onClick={() => handleDownload(file)}
                              />
                            )}
                          </div>
                        </div>
                      ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  const itemsForOrderDetails = [
    {
      key: 'trackingDetails',
      label: t('ordersPage.orderDetailsPage.trackingDetails'),
      children: collapseDetailsRendered(trackingDetails) as React.ReactNode,
    },
    {
      key: 'orderSummary',
      label: t('ordersPage.orderDetailsPage.orderSummary'),
      children: collapseDetailsRendered(orderSummaryColumn) as React.ReactNode,
    },
  ];

  const collectionLocationDetailsRendered = () => {
    return (
      <div className="flex flex-col gap-1 pt-1.5">
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.collectionCompanyName}
        </h2>
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.collectionContactName}
        </h2>
        <div className="flex items-start lg:items-center text-primary-900 font-medium text-text-md leading-8 flex-col lg:flex-row">
          <div className="flex gap-1 items-center font-medium">
            <PhoneOutlinedIcon /> {orderDetails?.collectionPhone || t('common.notAvailable')}{' '}
            {orderDetails?.collectionPhoneExtension && (
              <div>
                {t('common.extension')}{' '}
                {orderDetails?.collectionPhoneExtension || t('common.notAvailable')}
              </div>
            )}
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#8080805e', height: '20px' }}
            className="hidden lg:block"
          />
          <div className="flex gap-1.5 items-center font-medium">
            <EmailOutlinedIcon /> {orderDetails?.collectionEmail || t('common.notAvailable')}
          </div>
        </div>
        <div className="text-text-md text-primary-900">
          {orderDetails?.collectionAddressLine1},
          {orderDetails?.collectionAddressLine2 && orderDetails?.collectionAddressLine2},{' '}
          {orderDetails?.collectionCity}, {orderDetails?.collectionProvince},{' '}
          {orderDetails?.collectionPostalCode}, {orderDetails?.collectionCountry}
        </div>
      </div>
    );
  };

  const deliveryLocationDetailsRendered = () => {
    return (
      <div className="flex flex-col gap-1 pt-1.5">
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.deliveryCompanyName || t('common.notAvailable')}
        </h2>
        <h2 className="text-primary-900 font-semibold text-text-md">
          {orderDetails?.deliveryContactName || t('common.notAvailable')}
        </h2>
        <div className="flex items-start lg:items-center text-primary-900 font-medium text-text-md leading-8 flex-col lg:flex-row">
          <div className="flex gap-1 items-center font-medium">
            <PhoneOutlinedIcon /> {orderDetails?.deliveryPhone || t('common.notAvailable')}{' '}
            {orderDetails?.deliveryPhoneExtension && (
              <div>
                {t('common.extension')}
                {orderDetails?.deliveryPhoneExtension || t('common.notAvailable')}
              </div>
            )}
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#8080805e', height: '20px' }}
            className="hidden lg:block"
          />
          <div className="flex gap-1 items-center font-medium">
            <EmailOutlinedIcon /> {orderDetails?.deliveryEmail || t('common.notAvailable')}
          </div>
        </div>
        <div className="text-text-md text-primary-900">
          {orderDetails?.deliveryAddressLine1},{' '}
          {orderDetails?.deliveryAddressLine2 && orderDetails?.deliveryAddressLine2},{' '}
          {orderDetails?.deliveryCity}, {orderDetails?.deliveryProvince},{' '}
          {orderDetails?.deliveryPostalCode}, {orderDetails?.deliveryCountry}
        </div>
      </div>
    );
  };
  const navigate = useNavigate();

  const orderBillPDFViewHandler = async (billType: OrderBills) => {
    try {
      setLoadingBillType(billType);
      const response = await orderService.getById<Blob>(
        `${orderDetails?.id}/bills/${billType}`,
        undefined,
        {
          responseType: 'blob',
        }
      );
      blobToUrlNavigation(response);
    } catch (error) {
      // handle error (handled by error code)
    } finally {
      setLoadingBillType(null);
    }
  };

  return (
    <>
      <SendTrackingLinkModal
        order={orderDetails}
        isOpen={isTrackingModelOpen}
        setIsOpen={setIsTrackingModelOpen}
      />
      {error ? (
        <NotFound404
          title={t('ordersPage.orderDetailsPage.notFoundTitle')}
          description={t('ordersPage.orderDetailsPage.notFoundDescription')}
          extraNode={
            <Button
              className="h-[40px] px-8 hover:!text-white hover:!bg-primary-600"
              onClick={() => navigate(ROUTES.ORDER.LISTING)}
            >
              {t('ordersPage.orderDetailsPage.goBack')}
            </Button>
          }
        />
      ) : (
        <div className="flex print:!max-h-fit max-h-[87.5vh] overflow-y-scroll flex-col gap-4 px-6 print:px-2 print:font-Inter">
          <header className="flex justify-between items-center gap-3 pt-5">
            <div className="flex flex-col gap-1">
              <PageHeadingComponent
                title={`${t('ordersPage.orderDetailsPage.pageTitle')}#${orderDetails?.trackingNumber || t('common.notAvailable')}`}
                onBackClick={() => window.history.back()}
                isChildComponent
              />
              <h3 className="leading-6 font-medium text-primary-900 print:!ml-0 ml-12">
                {orderDetails?.status === OrderStatusEnums.DRAFT
                  ? t('ordersPage.orderDetailsPage.draftStatus')
                  : t('ordersPage.orderDetailsPage.requestSubmitted')}
              </h3>
            </div>
            {/* //TODO: Add approve order functionality in V2 */}
            {/* <div className="print:!hidden text-center bg-primary-600 print:!bg-primary-600 font-semibold py-2.5 print:!border px-14 text-[#e5e7eb] rounded-lg">
              {t('ordersPage.orderDetailsPage.approve')}
            </div> */}
          </header>
          <div className="print:!hidden flex gap-4 w-full flex-wrap justify-end">
            {orderDetails?.status !== OrderStatusEnums.DRAFT &&
              orderDetails?.status !== OrderStatusEnums.CANCELLED &&
              orderDetails?.status !== OrderStatusEnums.CANCELLEDBILLABLE && (
                <>
                  <Button
                    className="h-[44px] px-8 hover:!text-white hover:!bg-primary-600 font-medium"
                    onClick={() => setIsTrackingModelOpen(true)}
                  >
                    {t('ordersPage.orderDetailsPage.sendTrackingLink')}
                  </Button>
                </>
              )}
            {orderDetails?.status !== 'Completed' &&
              orderDetails?.status !== 'Cancelled' &&
              orderDetails?.status !== 'Cancelled Billable' && (
                <Button
                  onClick={() => navigate(`${ROUTES.ORDER.ORDER_ENTRY}/${orderDetails?.id}`)}
                  className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
                >
                  <Icon component={() => <EditPopupIcon bool={false} />} />{' '}
                  {t('ordersPage.orderDetailsPage.editOrder')}
                </Button>
              )}
            {orderDetails?.status !== OrderStatusEnums.DRAFT && (
              <>
                <Button
                  className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
                  onClick={() => {
                    document.fonts.ready.then(() => {
                      window.print();
                    });
                  }}
                >
                  <Icon component={() => <PrinterIcon bool={false} />} />
                  {t('ordersPage.orderDetailsPage.printThisPage')}
                </Button>
                <Button
                  className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
                  onClick={() => orderBillPDFViewHandler(OrderBills.SHIPPING_LABEL)}
                  loading={loadingBillType === OrderBills.SHIPPING_LABEL}
                  disabled={loadingBillType !== null}
                >
                  <Icon component={() => <PrinterIcon bool={false} />} />
                  {t('ordersPage.orderDetailsPage.printShippingLabel')}
                </Button>
                <Button
                  className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
                  onClick={() => orderBillPDFViewHandler(OrderBills.WAY_BILL)}
                  loading={loadingBillType === OrderBills.WAY_BILL}
                  disabled={loadingBillType !== null}
                >
                  <Icon component={() => <PrinterIcon bool={false} />} />
                  {t('ordersPage.orderDetailsPage.printWayBill')}
                </Button>
                <Button
                  className="h-[44px] px-8 text-primary-900 hover:!text-white hover:!bg-primary-600 font-medium"
                  onClick={() => orderBillPDFViewHandler(OrderBills.BILL_OF_LADING)}
                  loading={loadingBillType === OrderBills.BILL_OF_LADING}
                  disabled={loadingBillType !== null}
                >
                  <Icon component={() => <PrinterIcon bool={false} />} />
                  {t('ordersPage.orderDetailsPage.printBillOfLading')}
                </Button>
              </>
            )}
          </div>
          <OrderStatusSteps orderDetails={orderDetails as any} />
          <main className="pb-4">
            <CustomCollapse
              defaultActiveKey={['trackingDetails', 'orderSummary']}
              items={itemsForOrderDetails}
            />
            <div className="flex gap-4 mt-4 flex-col lg:flex-row">
              <CustomCollapse
                defaultActiveKey={['delivery']}
                className="w-full"
                collapseProps={{ collapsible: 'icon' }}
                collapsePanelProps={{
                  showArrow: false,
                  key: 'delivery',
                  header: t('ordersPage.orderDetailsPage.deliveryLocation'),
                }}
                items={[
                  {
                    key: 'delivery',
                    label: t('ordersPage.orderDetailsPage.deliveryLocation'),
                    children: deliveryLocationDetailsRendered(),
                  },
                ]}
              />
              <CustomCollapse
                defaultActiveKey={['pickup']}
                className="w-full"
                collapseProps={{ collapsible: 'icon' }}
                collapsePanelProps={{
                  showArrow: false,
                  key: 'pickup',
                  header: t('ordersPage.orderDetailsPage.pickupLocation'),
                }}
                items={[
                  {
                    key: 'pickup',
                    label: t('ordersPage.orderDetailsPage.pickupLocation'),
                    children: collectionLocationDetailsRendered(),
                  },
                ]}
              />
            </div>
          </main>
        </div>
      )}
    </>
  );
};

export default OrderDetailsPage;
