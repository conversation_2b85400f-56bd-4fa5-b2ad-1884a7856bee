import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import '@/styles/global.css';
import { LanguageProvider } from '@/contexts/LanguageContext.tsx';
import ErrorBoundary from '@/lib/errorHandling/ErrorBoundary.tsx';
import { logger } from '@lib/logger/logger.ts';
import { LogLevel } from '@lib/logger/types.ts';

logger.configure({
  minLevel: LogLevel.DEBUG,
  enabled: import.meta.env.MODE !== 'production',
  preserveConsole: true,
});

createRoot(document.getElementById('root')!).render(
  <>
    {/* <PostHogProvider
      apiKey={import.meta.env.VITE_PUBLIC_POSTHOG_KEY}
      options={{
        api_host: 'https://us.i.posthog.com',
        debug: import.meta.env.MODE === 'development',
      }}
    > */}
    <ErrorBoundary>
      <LanguageProvider>
        <App />
      </LanguageProvider>
    </ErrorBoundary>
    {/* </PostHogProvider> */}
  </>
);
