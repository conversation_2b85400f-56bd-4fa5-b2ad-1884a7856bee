import React from 'react';
import { RouteObject, useRoutes, Outlet } from 'react-router-dom';
import { CommonRoutes } from './CommonRoutes';
import ProtectedRoute from './ProtectedRoute';
import Layout from '@/components/layout/mainLayout';
import { OrderRoutes } from './OrderRoutes';
import { AddressesRoutes } from './Addresses';
import { InvoicesRoutes } from './InvoicesRoutes';
import { ContactsRoutes } from './ContactsRoutes';
import Header from '@/components/layout/header/header';
import OrderTracking from '@/pages/orders/orderTracking/orderTracking';
import { ROUTES } from '@/constant/RoutesConstant';
import Footer from '@/components/layout/footer';

// Centralized Header wrapper that doesn't require authentication
const CentralizedHeaderWrapper: React.FC = () => {
  return (
    <ProtectedRoute
      element={
        <Layout>
          <Outlet />
        </Layout>
      }
    />
  );
};

const AppRoutes: React.FC = () => {
  const routes: RouteObject[] = [
    ...CommonRoutes,
    {
      path: '/',
      element: <CentralizedHeaderWrapper />, // Apply Header to all routes
      children: [...OrderRoutes, ...AddressesRoutes, ...InvoicesRoutes, ...ContactsRoutes],
    },
    {
      path: ROUTES.ORDER.ORDER_TRACKING,
      element: (
        <>
          <Header showLogoOnly />
          <OrderTracking />
          <Footer />
        </>
      ),
    },
  ];

  const Router = useRoutes(routes);

  return <>{Router}</>;
};

export default AppRoutes;
