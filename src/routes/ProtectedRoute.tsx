import { Navigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { URoles } from '@/types/enums/Roles';
import GlobalAppLoadingOverlay from '@/components/common/spinner/GlobalAppLoadingOverlay';
import { useGetCurrentUser } from '@/api/auth/auth.service';

interface ProtectedRouteProps {
  element: React.ReactElement;
  allowedRoles?: URoles[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ element }) => {
  const { data: userInfo, isFetching, isLoading } = useGetCurrentUser();

  if (isFetching || isLoading) {
    return <GlobalAppLoadingOverlay />;
  }

  if (!userInfo) {
    return <Navigate to={ROUTES.COMMON.LOGIN} replace />;
  }

  return element;
};

export default ProtectedRoute;
