import { useGetCurrentUser } from '@/api/auth/auth.service';
import GlobalAppLoadingOverlay from '@/components/common/spinner/GlobalAppLoadingOverlay';
import { ROUTES } from '@/constant/RoutesConstant';
import { Navigate } from 'react-router-dom';

interface PublicRouteProps {
  children: JSX.Element;
}

export const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const { data: userInfo, isFetching, isLoading } = useGetCurrentUser();

  if (isFetching || isLoading) {
    return <GlobalAppLoadingOverlay />;
  }
  return !userInfo ? children : <Navigate to={ROUTES.ORDER.ORDER_ENTRY} />;
};
