import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { DEV_CONFIG } from '@/config/devConfig';

// Configure dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

export const DEFAULT_TIMEZONE = 'Asia/Calcutta';

export const toTenantTimezone = (date: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
  return dayjs(date).tz(DEFAULT_TIMEZONE);
};

export const calculateTime = (startTime: dayjs.Dayjs, endTime: dayjs.Dayjs) => {
  const adjustedEnd = endTime.isBefore(startTime) ? endTime.add(1, 'day') : endTime;

  const duration = adjustedEnd.diff(startTime, 'minute');
  const totalHours = Math.floor(duration / 60);
  const totalMinutes = duration % 60;

  const timeString = `${totalHours}h ${totalMinutes}min`;

  return { totalHours, totalMinutes: timeString };
};

export const dateFormatter = (value: string, formatter?: string) => {
  const config = DEV_CONFIG.DATE_FORMATE_WITH_TIME_24H;
  return value && dayjs(value).isValid()
    ? dayjs(value)
        .utc(true)
        .format(formatter || config)
    : '';
};

export const convertInUTC = (value: Dayjs) => {
  return dayjs(value).utc().toISOString();
};

export const getStartOfDateInUTC = (value: Dayjs) => {
  return dayjs.utc(value.format('YYYY-MM-DD')).startOf('day').toISOString();
};
