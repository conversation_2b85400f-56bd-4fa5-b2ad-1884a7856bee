import { IUIConfigurationItem } from '@/api/customerSettings/customerSettings.types';

export interface FieldConfiguration {
    isVisible: boolean;
    isRequired: boolean;
    placeholder?: string;
    tooltip?: string;
}

export interface AddressFieldConfig {
    [fieldIdentifier: string]: FieldConfiguration;
}

/**
 * Processes customer settings to determine field visibility and requirements
 * @param customerSettings - The customer settings array from the API
 * @param addressType - Either 'pickupLocation' or 'deliveryLocation'
 * @returns Configuration object for address fields
 * 
 * Logic:
 * - If isVisible is true, field will be shown
 * - If isRequired is true, field will be required
 * - If both are false, field will be hidden and not required
 */
export const processCustomerSettings = (
    customerSettings: IUIConfigurationItem[],
    addressType: string
): AddressFieldConfig => {
    const fieldConfig: AddressFieldConfig = {};
    console.log(customerSettings, 'customerSettings123');
    // Find the address type configuration (pickup or delivery)
    const addressConfig = customerSettings.find(
        (config) => config.fieldIdentifier === addressType
    );
    console.log(addressConfig, 'addressConfig');
    if (!addressConfig || !addressConfig.children) {
        console.log('No address config or children found, returning empty config');
        return fieldConfig;
    }

    // Process each child field
    addressConfig.children.forEach((child) => {
        const { fieldIdentifier, isVisible, isRequired, placeholder, tooltip } = child;

        // Use isVisible and isRequired directly from customer settings
        // Map field identifiers to form field names
        let formFieldName = fieldIdentifier;
        
        // Address field mappings
        if (fieldIdentifier === 'comments') {
            formFieldName = 'notes';
        }
        
        // Package field mappings
        if (fieldIdentifier === 'weight') {
            formFieldName = 'totalWeight';
        } else if (fieldIdentifier === 'packageImages') {
            formFieldName = 'imageName';
        } else if (fieldIdentifier === 'dimensions') {
            // Dimensions affects multiple fields: length, width, height, cubicDimension
            // We'll apply the same config to all dimension-related fields
            fieldConfig['length'] = {
                isVisible,
                isRequired,
                placeholder,
                tooltip,
            };
            fieldConfig['width'] = {
                isVisible,
                isRequired,
                placeholder,
                tooltip,
            };
            fieldConfig['height'] = {
                isVisible,
                isRequired,
                placeholder,
                tooltip,
            };
            fieldConfig['cubicDimension'] = {
                isVisible,
                isRequired,
                placeholder,
                tooltip,
            };
            return; // Skip the default assignment below
        }

        fieldConfig[formFieldName] = {
            isVisible,
            isRequired,
            placeholder,
            tooltip,
        };
    });
    console.log(fieldConfig, 'fieldConfig');
    return fieldConfig;
};

/**
 * Gets field configuration for a specific field
 * @param fieldConfig - The field configuration object
 * @param fieldIdentifier - The field identifier to get config for
 * @returns Field configuration or default values
 */
export const getFieldConfig = (
    fieldConfig: AddressFieldConfig | undefined,
    fieldIdentifier: string
): FieldConfiguration => {
    if (!fieldConfig) {
        return {
            isVisible: true, // Default to visible if no fieldConfig provided
            isRequired: false, // Default to not required if no fieldConfig provided
        };
    }

    const config = fieldConfig[fieldIdentifier];
    console.log(config, 'config');
    if (!config) {
        return {
            isVisible: false, // Default to NOT visible if not configured
            isRequired: false, // Default to not required if not configured
        };
    }


    return config;
};

/**
 * Creates validation rules for a form field based on customer settings
 * @param fieldConfig - The field configuration
 * @param requiredMessage - The message to show when field is required
 * @returns Array of validation rules
 */
export const createValidationRules = (
    fieldConfig: FieldConfiguration,
    requiredMessage: string
) => {
    const rules = [];

    if (fieldConfig.isRequired) {
        rules.push({
            required: true,
            message: requiredMessage,
        });
    }

    return rules;
};
