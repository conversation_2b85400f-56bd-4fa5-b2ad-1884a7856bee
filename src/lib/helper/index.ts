import { calculateTime } from './dateHelper';
import { searchData } from './searchQuery';
import { isFormChangedHandler } from './formHelper';
import { getPaginationData } from './agGridHelper';
import { processCustomerSettings, getFieldConfig, createValidationRules } from './customerSettingsHelper';

export const getOrderStatusClassName = (status: string): string => {
  switch (status) {
    case 'Submitted':
      return 'primary-chip';
    case 'Draft':
      return 'grey-chip';
    case 'Cancelled':
      return 'error-chip';
    case 'InTransit':
    case 'Pending':
      return 'warning-chip';
    case 'Assigned':
      return 'primary-chip';
    case 'Completed':
      return 'success-chip';
    default:
      return 'grey-chip';
  }
};

export const downloadFromBlob = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};

export const blobToUrlNavigation = (blob: Blob) => {
  const url = window.URL.createObjectURL(blob);
  window.open(url, '_blank');
};

export { calculateTime, searchData, isFormChangedHandler, getPaginationData, processCustomerSettings, getFieldConfig, createValidationRules };
