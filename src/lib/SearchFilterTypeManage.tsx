import dayjs, { Dayjs, isDayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getStartOfDateInUTC } from './helper/dateHelper';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import { DEV_CONFIG } from '@/config/devConfig';

dayjs.extend(utc);

export const TransformFilters = (
  filters: Array<{ field: string; operator?: string; value: string }>
): Record<string, unknown> =>
  filters?.reduce(
    (result, filter) => {
      const key = filter.field.toLowerCase();
      result[key] = filter.operator
        ? { operator: filter.operator, value: filter.value }
        : filter.value === 'true';
      return result;
    },
    {} as Record<string, unknown>
  );

export const escapeRegex = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

export const highlightText = (value: string, search: string) => {
  if (!search) return value;
  const escapedSearch = escapeRegex(search);
  const regex = new RegExp(`(${escapedSearch})`, 'gi');

  const parts = String(value || '').split(regex);

  return (
    <span
      dangerouslySetInnerHTML={{
        __html: parts.map((part) => (regex.test(part) ? `<mark>${part}</mark>` : part)).join(''),
      }}
    />
  );
};

export const advanceFilterObjectMapper = async (
  filterObject: IAssignedFilters[],
  config?: { defaultOperator: string }
): Promise<{ [key: string]: string }> => {
  const queryParams = filterObject.reduce(
    (acc, { field, operator, value }) => {
      const oldValue = value;
      const defaultOperator = config?.defaultOperator || 'eq';

      if (isDayjs(value)) {
        value = getStartOfDateInUTC(value.startOf('day') as Dayjs);
      }

      // Getting start date (value[0]) and end date (value[1]) for "between" filter
      if (Array.isArray(value)) {
        value = value.map((v, idx) =>
          isDayjs(v)
            ? idx === 0
              ? getStartOfDateInUTC(dayjs(v).startOf('day'))
              : dayjs((v as Dayjs).endOf('day') as Dayjs)
                  .utc()
                  .toISOString()
            : v
        );
      }

      if (operator === 'eq' && isDayjs(oldValue)) {
        const startOfDay = getStartOfDateInUTC(oldValue);
        const endOfDay = dayjs((oldValue as Dayjs).endOf('day') as Dayjs)
          .utc()
          .toISOString();

        acc[`${field}:gte`] = startOfDay;
        acc[`${field}:lte`] = endOfDay;
        return acc;
      }

      // Handle between operator separately
      if (operator === 'between' && Array.isArray(value) && value.length === 2) {
        acc[`${field}:gte`] = value[0];
        acc[`${field}:lte`] = value[1];
      } else {
        if (
          (operator === 'gte' || operator === 'gt') &&
          `${field}:${operator || defaultOperator}` in acc
        ) {
          acc[`${field}:${operator || defaultOperator}`] =
            Number(acc[`${field}:${operator || defaultOperator}`]) > Number(value)
              ? acc[`${field}:${operator || defaultOperator}`]
              : (value as string);
        } else if (
          (operator === 'lte' || operator === 'lt') &&
          `${field}:${operator || defaultOperator}` in acc
        ) {
          acc[`${field}:${operator || defaultOperator}`] =
            Number(acc[`${field}:${operator || defaultOperator}`]) < Number(value)
              ? acc[`${field}:${operator || defaultOperator}`]
              : (value as string);
        } else acc[`${field}:${operator || defaultOperator}`] = value as string;
      }
      return acc;
    },
    {} as { [key: string]: string }
  );

  return queryParams;
};

export const maskQuickFilterData = (
  filters: IAssignedFilters[],
  replacementValueObject?: Record<string, string>
) => {
  const quickFilterData = filters?.map((filter) => {
    if (replacementValueObject && replacementValueObject[filter.value]) {
      return {
        ...filter,
        value: replacementValueObject[filter.value],
      };
    }
    if (isDayjs(filter.value)) {
      return {
        ...filter,
        value: filter.value.format(DEV_CONFIG.DATE_FORMATE_WITHOUT_TIME),
      };
    } else if (Array.isArray(filter.value) && isDayjs(filter.value[0]) && filter.value[1]) {
      return {
        ...filter,
        value: [
          dayjs(filter.value[0]).format(DEV_CONFIG.DATE_FORMATE_WITHOUT_TIME),
          dayjs(filter.value[1]).format(DEV_CONFIG.DATE_FORMATE_WITHOUT_TIME),
        ],
      };
    } else {
      return filter;
    }
  });
  return quickFilterData;
};
