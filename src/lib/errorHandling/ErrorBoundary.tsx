import { DEV_CONFIG } from '@/config/devConfig';
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({ error, errorInfo });
    // Here you can add any additional error logging logic
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (DEV_CONFIG.SHOW_DETAILED_ERRORS) {
        return (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.85)',
              color: '#e6e6e6',
              fontFamily: 'Menlo, Consolas, monospace',
              fontSize: '13px',
              padding: '2rem',
              overflow: 'auto',
              zIndex: 9999,
            }}
          >
            <div style={{ marginBottom: '1rem' }}>
              <span style={{ color: '#ff5555', fontWeight: 'bold' }}>Error</span>
              <span style={{ marginLeft: '1rem', color: '#ffffff' }}>
                {this.state.error && this.state.error.toString()}
              </span>
            </div>
            <div style={{ marginBottom: '1rem', color: '#ffffff' }}>
              <span style={{ color: '#ff8c00', fontWeight: 'bold' }}>Stack</span>
            </div>
            <pre
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                padding: '1rem',
                borderRadius: '4px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}
            >
              {this.state.error && this.state.error.stack}
            </pre>
            <div style={{ marginTop: '1rem', color: '#ffffff' }}>
              <span style={{ color: '#ff8c00', fontWeight: 'bold' }}>Component Stack</span>
            </div>
            <pre
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                padding: '1rem',
                borderRadius: '4px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}
            >
              {this.state.errorInfo && this.state.errorInfo.componentStack}
            </pre>
          </div>
        );
      } else {
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100vh',
              textAlign: 'center',
              backgroundColor: '#f8f9fa',
              color: '#343a40',
              padding: '20px',
            }}
          >
            <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>Oops! Something went wrong.</h1>
            <p style={{ fontSize: '1rem', marginBottom: '1rem' }}>
              We've encountered an unexpected error. Our team has been notified and is working on a
              solution.
            </p>
            <button
              onClick={() => window.location.reload()}
              style={{
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '1rem',
              }}
            >
              Reload Page
            </button>
          </div>
        );
      }
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

export const withErrorBoundary = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithErrorBoundary: React.FC<P> = (props) => (
    <ErrorBoundary>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundary.displayName = `WithErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return WithErrorBoundary;
};
