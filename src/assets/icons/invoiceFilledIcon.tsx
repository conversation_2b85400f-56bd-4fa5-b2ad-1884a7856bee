import { PRIMARY } from '@/styles/colorConstants';

export const InvoiceFilledIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.25 1.5H4.5C2.25 1.5 1.5 2.8425 1.5 4.5V5.25V15.75C1.5 16.3725 2.205 16.725 2.7 16.35L3.9825 15.39C4.2825 15.165 4.7025 15.195 4.9725 15.465L6.2175 16.7175C6.51 17.01 6.99 17.01 7.2825 16.7175L8.5425 15.4575C8.805 15.195 9.225 15.165 9.5175 15.39L10.8 16.35C11.295 16.7175 12 16.365 12 15.75V3C12 2.175 12.675 1.5 13.5 1.5H5.25ZM4.4775 10.5075C4.065 10.5075 3.7275 10.17 3.7275 9.7575C3.7275 9.345 4.065 9.0075 4.4775 9.0075C4.89 9.0075 5.2275 9.345 5.2275 9.7575C5.2275 10.17 4.89 10.5075 4.4775 10.5075ZM4.4775 7.5075C4.065 7.5075 3.7275 7.17 3.7275 6.7575C3.7275 6.345 4.065 6.0075 4.4775 6.0075C4.89 6.0075 5.2275 6.345 5.2275 6.7575C5.2275 7.17 4.89 7.5075 4.4775 7.5075ZM9 10.32H6.75C6.4425 10.32 6.1875 10.065 6.1875 9.7575C6.1875 9.45 6.4425 9.195 6.75 9.195H9C9.3075 9.195 9.5625 9.45 9.5625 9.7575C9.5625 10.065 9.3075 10.32 9 10.32ZM9 7.32H6.75C6.4425 7.32 6.1875 7.065 6.1875 6.7575C6.1875 6.45 6.4425 6.195 6.75 6.195H9C9.3075 6.195 9.5625 6.45 9.5625 6.7575C9.5625 7.065 9.3075 7.32 9 7.32Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M13.5075 1.5V2.625C14.0025 2.625 14.475 2.8275 14.82 3.165C15.18 3.5325 15.375 4.005 15.375 4.5V6.315C15.375 6.87 15.1275 7.125 14.565 7.125H13.125V3.0075C13.125 2.7975 13.2975 2.625 13.5075 2.625V1.5ZM13.5075 1.5C12.675 1.5 12 2.175 12 3.0075V8.25H14.565C15.75 8.25 16.5 7.5 16.5 6.315V4.5C16.5 3.675 16.1625 2.925 15.6225 2.3775C15.075 1.8375 14.3325 1.5075 13.5075 1.5C13.515 1.5 13.5075 1.5 13.5075 1.5Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
    </svg>
  );
};
