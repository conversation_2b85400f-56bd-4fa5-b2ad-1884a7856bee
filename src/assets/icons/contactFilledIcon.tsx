import { PRIMARY } from '@/styles/colorConstants';

export const ContactFilledIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.75 1.5C4.785 1.5 3.1875 3.0975 3.1875 5.0625C3.1875 6.99 4.695 8.55 6.66 8.6175C6.72 8.61 6.78 8.61 6.825 8.6175C6.84 8.6175 6.8475 8.6175 6.8625 8.6175C6.87 8.6175 6.87 8.6175 6.8775 8.6175C8.7975 8.55 10.305 6.99 10.3125 5.0625C10.3125 3.0975 8.715 1.5 6.75 1.5Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M10.56 10.6125C8.46747 9.2175 5.05497 9.2175 2.94747 10.6125C1.99497 11.25 1.46997 12.1125 1.46997 13.035C1.46997 13.9575 1.99497 14.8125 2.93997 15.4425C3.98997 16.1475 5.36997 16.5 6.74997 16.5C8.12997 16.5 9.50997 16.1475 10.56 15.4425C11.505 14.805 12.03 13.95 12.03 13.02C12.0225 12.0975 11.505 11.2425 10.56 10.6125Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M14.9925 5.50501C15.1125 6.96001 14.0775 8.23501 12.645 8.40751C12.6375 8.40751 12.6375 8.40751 12.63 8.40751H12.6075C12.5625 8.40751 12.5175 8.40751 12.48 8.42251C11.7525 8.46001 11.085 8.22751 10.5825 7.80001C11.355 7.11001 11.7975 6.07501 11.7075 4.95001C11.655 4.34251 11.445 3.78751 11.13 3.31501C11.415 3.17251 11.745 3.08251 12.0825 3.05251C13.5525 2.92501 14.865 4.02001 14.9925 5.50501Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M16.4924 12.4425C16.4324 13.17 15.9674 13.8 15.1874 14.2275C14.4374 14.64 13.4924 14.835 12.5549 14.8125C13.0949 14.325 13.4099 13.7175 13.4699 13.0725C13.5449 12.1425 13.1024 11.25 12.2174 10.5375C11.7149 10.14 11.1299 9.82499 10.4924 9.59249C12.1499 9.11249 14.2349 9.43499 15.5174 10.47C16.2074 11.025 16.5599 11.7225 16.4924 12.4425Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
    </svg>
  );
};
