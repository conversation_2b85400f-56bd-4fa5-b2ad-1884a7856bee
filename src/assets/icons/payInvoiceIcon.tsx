import React from 'react';

export const PayInvoiceIcon = (_props: React.SVGProps<SVGSVGElement> & { bool?: boolean }) => {
  return (
    <>
      <svg
        width="18"
        height="18"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.12451 6.9502H10.6919C11.5094 6.95042 12.2083 7.64499 12.2085 8.5332C12.2085 8.56322 12.1958 8.59453 12.1704 8.62012C12.1449 8.64567 12.1135 8.65811 12.0835 8.6582C12.0533 8.6582 12.0222 8.64576 11.9966 8.62012C11.9709 8.59447 11.9585 8.56335 11.9585 8.5332C11.9583 7.8405 11.4325 7.20043 10.6919 7.2002H9.12451C8.47226 7.20039 8.0415 7.77046 8.0415 8.35059C8.04153 8.59898 8.07102 8.87396 8.24561 9.09766C8.41544 9.31503 8.66166 9.39541 8.78369 9.43848H8.78467L11.2935 10.3135L11.2954 10.3145C11.5284 10.3945 11.7475 10.5038 11.9106 10.6855C12.0634 10.8558 12.2084 11.1355 12.2085 11.6416C12.2085 12.4316 11.5909 13.042 10.8745 13.042H9.30811C8.49052 13.0419 7.79175 12.3472 7.7915 11.459C7.7915 11.4288 7.80395 11.3967 7.82959 11.3711C7.85519 11.3456 7.88646 11.334 7.9165 11.334C7.94655 11.334 7.97782 11.3456 8.00342 11.3711C8.02906 11.3967 8.0415 11.4288 8.0415 11.459C8.04174 12.1517 8.56739 12.7919 9.30811 12.792H10.8745C11.5269 12.792 11.9585 12.2218 11.9585 11.6416C11.9585 11.3932 11.929 11.1183 11.7544 10.8945C11.5846 10.677 11.3384 10.5968 11.2163 10.5537L11.2144 10.5527L8.70654 9.67773H8.70361C8.47076 9.59767 8.25144 9.48836 8.08838 9.30664C7.93574 9.13642 7.79158 8.8564 7.7915 8.35059C7.7915 7.56762 8.4097 6.95039 9.12451 6.9502Z"
          fill="#2D3484"
          stroke="#2D3484"
          className="cell-icons-stroke"
        />
        <path
          d="M10 6.125C10.0301 6.125 10.0613 6.13744 10.0869 6.16309C10.1126 6.18873 10.125 6.21985 10.125 6.25V13.75C10.125 13.7801 10.1126 13.8113 10.0869 13.8369C10.0613 13.8626 10.0301 13.875 10 13.875C9.96985 13.875 9.93873 13.8626 9.91309 13.8369C9.88744 13.8113 9.875 13.7801 9.875 13.75V6.25C9.875 6.21985 9.88744 6.18873 9.91309 6.16309C9.93873 6.13744 9.96985 6.125 10 6.125Z"
          fill="#2D3484"
          stroke="#2D3484"
          className="cell-icons-stroke"
        />
        <path
          d="M9.99951 1.5415C10.0296 1.5415 10.0608 1.55406 10.0864 1.57959C10.1121 1.60523 10.1245 1.63636 10.1245 1.6665C10.1245 1.69665 10.1121 1.72777 10.0864 1.75342C10.0608 1.77895 10.0296 1.7915 9.99951 1.7915C5.47363 1.79168 1.79168 5.47363 1.7915 9.99951C1.7915 14.5255 5.47352 18.2083 9.99951 18.2085C14.5257 18.2085 18.2085 14.5257 18.2085 9.99951C18.2086 9.96946 18.221 9.93815 18.2466 9.9126C18.2722 9.88718 18.3035 9.87451 18.3335 9.87451C18.3635 9.8746 18.3949 9.88705 18.4204 9.9126C18.446 9.93815 18.4584 9.96946 18.4585 9.99951C18.4585 14.665 14.665 18.4585 9.99951 18.4585C5.33414 18.4583 1.5415 14.6649 1.5415 9.99951C1.54168 5.33425 5.33425 1.54168 9.99951 1.5415Z"
          fill="#2D3484"
          stroke="#2D3484"
          className="cell-icons-stroke"
        />
        <path
          d="M14.1665 2.375C14.1967 2.375 14.2278 2.38744 14.2534 2.41309C14.2791 2.43873 14.2915 2.46985 14.2915 2.5V5.70801H17.4995C17.5295 5.70801 17.5608 5.72067 17.5864 5.74609C17.612 5.77165 17.6244 5.80296 17.6245 5.83301C17.6245 5.86316 17.6121 5.89428 17.5864 5.91992C17.5608 5.94557 17.5297 5.95801 17.4995 5.95801H14.1665C14.1364 5.95801 14.1052 5.94557 14.0796 5.91992C14.0541 5.89431 14.0415 5.86309 14.0415 5.83301V2.5C14.0415 2.46985 14.0539 2.43873 14.0796 2.41309C14.1052 2.38744 14.1364 2.375 14.1665 2.375Z"
          fill="#2D3484"
          stroke="#2D3484"
          className="cell-icons-stroke"
        />
        <path
          d="M18.334 1.54395C18.3696 1.54406 18.4004 1.55767 18.4219 1.5791C18.4432 1.60061 18.4561 1.63131 18.4561 1.66699C18.456 1.7027 18.4433 1.73339 18.4219 1.75488L14.2549 5.92188C14.2404 5.93633 14.2266 5.94503 14.2139 5.9502C14.2006 5.95555 14.1846 5.95897 14.167 5.95898C14.1493 5.95898 14.1334 5.95556 14.1201 5.9502C14.1074 5.94505 14.0936 5.93634 14.0791 5.92188C14.0577 5.90044 14.0441 5.86964 14.0439 5.83398C14.0439 5.79813 14.0576 5.76664 14.0791 5.74512L18.2451 1.5791C18.2666 1.55757 18.2981 1.54395 18.334 1.54395Z"
          fill="#2D3484"
          stroke="#2D3484"
          className="cell-icons-stroke"
        />
      </svg>
    </>
  );
};
