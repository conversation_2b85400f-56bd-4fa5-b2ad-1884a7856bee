import { PRIMARY } from '@/styles/colorConstants';

export const OrderListsFilledIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.43 4.18501H14.13L11.595 1.65001C11.3925 1.44751 11.0625 1.44751 10.8525 1.65001C10.65 1.85251 10.65 2.18251 10.8525 2.39251L12.645 4.18501H5.355L7.1475 2.39251C7.35 2.19001 7.35 1.86001 7.1475 1.65001C6.945 1.44751 6.615 1.44751 6.405 1.65001L3.8775 4.18501H3.5775C2.9025 4.18501 1.5 4.18501 1.5 6.10501C1.5 6.83251 1.65 7.31251 1.965 7.62751C2.145 7.81501 2.3625 7.91251 2.595 7.96501C2.8125 8.01751 3.045 8.02501 3.27 8.02501H14.73C14.9625 8.02501 15.18 8.01001 15.39 7.96501C16.02 7.81501 16.5 7.36501 16.5 6.10501C16.5 4.18501 15.0975 4.18501 14.43 4.18501Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M14.2875 9H3.65249C3.18749 9 2.83499 9.4125 2.90999 9.87L3.53999 13.725C3.74999 15.015 4.31249 16.5 6.80999 16.5H11.0175C13.545 16.5 13.995 15.2325 14.265 13.815L15.0225 9.8925C15.1125 9.4275 14.76 9 14.2875 9ZM8.99999 14.625C7.24499 14.625 5.81249 13.1925 5.81249 11.4375C5.81249 11.13 6.06749 10.875 6.37499 10.875C6.68249 10.875 6.93749 11.13 6.93749 11.4375C6.93749 12.5775 7.85999 13.5 8.99999 13.5C10.14 13.5 11.0625 12.5775 11.0625 11.4375C11.0625 11.13 11.3175 10.875 11.625 10.875C11.9325 10.875 12.1875 11.13 12.1875 11.4375C12.1875 13.1925 10.755 14.625 8.99999 14.625Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
    </svg>
  );
};

<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_14906_325921)">
    <path
      d="M15.4651 6.3375C14.6776 2.8725 11.6551 1.3125 9.00007 1.3125C9.00007 1.3125 9.00007 1.3125 8.99257 1.3125C6.34507 1.3125 3.31507 2.865 2.52757 6.33C1.65007 10.2 4.02007 13.4775 6.16507 15.54C6.96007 16.305 7.98007 16.6875 9.00007 16.6875C10.0201 16.6875 11.0401 16.305 11.8276 15.54C13.9726 13.4775 16.3426 10.2075 15.4651 6.3375ZM9.00007 10.095C7.69507 10.095 6.63757 9.0375 6.63757 7.7325C6.63757 6.4275 7.69507 5.37 9.00007 5.37C10.3051 5.37 11.3626 6.4275 11.3626 7.7325C11.3626 9.0375 10.3051 10.095 9.00007 10.095Z"
      fill="#2D3484"
    />
  </g>
  <defs>
    <clipPath id="clip0_14906_325921">
      <rect width="18" height="18" fill="white" />
    </clipPath>
  </defs>
</svg>;
