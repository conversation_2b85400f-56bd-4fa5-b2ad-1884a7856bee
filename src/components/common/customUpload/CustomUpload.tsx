import { Upload, Button, Form } from 'antd';
import { memo, useRef } from 'react';
import CustomTooltip from '../customTooltip/CustomTooltip';
import { infoCircleOutlined } from '@/assets';
import { useLanguage } from '@/hooks/useLanguage';
import { ICustomUploadProps } from './customUpload.types';
import { UploadFile } from 'antd/lib';

interface CustomUploadWithFileListProps extends ICustomUploadProps {
  fileList?: UploadFile[];
  isDisabled?: boolean;
}

const CustomUpload = ({
  form,
  uploadComponentProps,
  name,
  placeholder,
  label,
  uploadButtonText,
  fileList = [],
}: CustomUploadWithFileListProps) => {
  const uploadButtonRef = useRef<HTMLButtonElement>(null);

  const handleCustomClick = () => {
    if (uploadButtonRef.current) {
      uploadButtonRef.current?.click();
    }
  };

  const { t } = useLanguage();

  const uploadFieldWatcher = Form.useWatch(name, form);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex  justify-end items-center gap-2 font-medium text-[#20363f] w-fit">
        {label || (
          <>
            <CustomTooltip>
              <img src={infoCircleOutlined} alt="info" />
            </CustomTooltip>
            {t('ordersPage.packagesTab.uploadImage')}
          </>
        )}
      </div>
      <div className="flex">
        <div
          className="border border-[#d9d9d9] w-full flex items-center px-3 rounded-tl-[6px] rounded-bl-[6px] border-r-0 cursor-pointer select-none"
          onClick={handleCustomClick}
        >
          {uploadFieldWatcher?.file?.name || (
            <span className="text-gray-400">
              {placeholder || t('ordersPage.packagesTab.chooseImage')}
            </span>
          )}
        </div>
        <Upload
          {...uploadComponentProps}
          fileList={fileList}
          onChange={(info) => {
            uploadComponentProps?.onChange && uploadComponentProps.onChange(info);
          }}
          maxCount={uploadComponentProps?.maxCount || 1}
          showUploadList={false}
        >
          <Button
            ref={uploadButtonRef}
            type="primary"
            className="h-[40px] text-[#090A1A] bg-[#F5F6FF] border-[#d9d9d9] rounded-tl-none rounded-bl-none hover:!bg-[#F5F6FF] hover:!text-[#090A1A]"
          >
            {uploadButtonText || t('ordersPage.packagesTab.uploadButtonText')}
          </Button>
        </Upload>
      </div>
    </div>
  );
};

export default memo(CustomUpload);
