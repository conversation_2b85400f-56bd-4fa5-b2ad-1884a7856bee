import { NotFound404SVGComponent } from '@/assets/icons/notfound404SVGComponent';
import Icon from '@ant-design/icons';

interface ILinkExpiredProps {
  title?: string;
  description?: string;
  extraNode?: React.ReactElement;
}

const LinkExpired = ({ title, description, extraNode }: ILinkExpiredProps) => {
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Icon component={NotFound404SVGComponent} />
      <div className="text-gray-400 font-semibold text-2xl mt-6">
        {title || 'Page Not Found'}
      </div>
      <div className="text-gray-400 font-medium text-base mt-2">
        {description || 'The link you are trying to access has been expired please try creating new'}
      </div>
      <div className="mt-2">{extraNode && extraNode}</div>
    </div>
  );
};

export default LinkExpired;

