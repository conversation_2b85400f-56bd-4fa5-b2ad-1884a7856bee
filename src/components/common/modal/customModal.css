.custom-modal-mask,
.custom-modal-with-full-height-mask {
  background-color: #0876a40a !important;
}
.custom-modal,
.custom-modal-with-full-height {
  width: 100% !important;
  max-width: 684px;
}
.custom-modal-body,
.custom-modal-with-full-height-body {
  padding: 16px 24px !important;
  max-height: 516px;
  overflow-y: scroll;
}
.custom-modal-with-full-height-body {
  padding: 16px 24px !important;
  max-height: max-content;
  overflow-y: scroll;
}

.custom-modal-content,
.custom-modal-with-full-height-content {
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden;
  font-family: var(--font-family);
}

.custom-modals-header,
.custom-modals-with-full-height-header {
  box-shadow: 0px -1px 0px 0px #cdd7db inset;
  @apply bg-[#F5F6FF] py-4 px-6;
}

.custom-modal-header,
.custom-modal-with-full-height-header {
  margin-bottom: 2px !important;
}

.custom-modal-close,
.custom-modal-with-full-height-close {
  top: 22px !important;
}

.custom-modal-footer,
.custom-modal-with-full-height-footer {
  box-shadow: 0px 1px 0px 0px #cdd7db inset !important;
  background-color: #f5f6ff !important;
  padding: 10px 16px !important;
}
