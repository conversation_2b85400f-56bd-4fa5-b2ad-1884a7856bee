import { Button, Form, Input, Spin } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useCallback, useMemo } from 'react';
import CustomModal from '../modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import { LumigoLogo } from '@/assets';
import Icon, { LoadingOutlined } from '@ant-design/icons';
import FacebookIcon from '@/assets/icons/facebookIcon';
import LinkedInIcon from '@/assets/icons/linkedInIcon';
import InstagramIcon from '@/assets/icons/instagramIcon';
import XIcon from '@/assets/icons/xIcon';
import YoutubeIcon from '@/assets/icons/youtubeIcon';
import { IOrder } from '@/api/order/order.types';
import { TrackingLinkServiceHook } from '@/api/trackingLink/useTrackingLink';
import { useNotificationManager } from '@/hooks/useNotificationManger';

import { tenantsHook } from '@/api/tenants/useTenants';
import { DEV_CONFIG } from '@/config/devConfig';

interface ISendTrackingLinkModalProps {
  order: IOrder | null | undefined;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const SendTrackingLinkModal = (props: ISendTrackingLinkModalProps) => {
  const { order, isOpen, setIsOpen } = props;

  const [form] = Form.useForm();
  const messageWatcher = Form.useWatch('message', form);
  const { t } = useLanguage();
  const notificationManager = useNotificationManager();
  const trackingLinkMutation = TrackingLinkServiceHook.useCreate( {
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Tracking link sent successfully',
      });
      setIsOpen(false);
      form.resetFields();
    },
    onError: () => {
      notificationManager.error({
        message: t('common.error'),
        description: 'Failed to send tracking link',
      });
    },
});  const {
    data: tenantDetails,
    isFetching,
    isLoading,
  } = tenantsHook.useEntity('tenantDetails', { staleTime: 60000 });

  const handleOrderTracking = useCallback(async () => {
    await trackingLinkMutation.mutateAsync({
      orderId: order?.id as string,
      email: form.getFieldValue('email') as string,
      message: form.getFieldValue('message') as string,
    });
  }, [order, form]);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="rounded-lg border-[#96A9B1]" onClick={() => setIsOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          form="tracking-link-form"
          htmlType="submit"
          type="primary"
          onClick={handleOrderTracking}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {t('ordersPage.orderDetailsPage.send')}
        </Button>
      </footer>
    ),
    [setIsOpen, t]
  );

  const renderTenantLogo = () => {
    if (isFetching || isLoading) {
      return (
        <div className="flex justify-center items-center w-3/4 h-20">
          <Spin indicator={<LoadingOutlined spin className="text-primary-600 block" />} />
        </div>
      );
    }
    const tenantLogo = tenantDetails?.brandLogo ? tenantDetails?.brandLogo : LumigoLogo;
    const tenantName = tenantDetails?.name ? tenantDetails?.name : DEV_CONFIG.APP_NAME;

    return (
      <img
        src={tenantLogo}
        alt={tenantName}
        className={`${tenantDetails?.brandLogo ? 'h-[50px]' : 'h-[40px]'} `}
      />
    );
  };

  const renderSocialMediaIcons = () => {
    const socialMediaLinks = [
      {
        icon: FacebookIcon,
        domain: 'https://www.facebook.com/',
        url: tenantDetails?.faceBookUrl || undefined,
      },
      {
        icon: LinkedInIcon,
        domain: 'https://www.linkedin.com/in/',
        url: tenantDetails?.linkedInUrl || undefined,
      },
      {
        icon: InstagramIcon,
        domain: 'https://www.instagram.com/',
        url: tenantDetails?.instagramUrl || undefined,
      },
      { icon: XIcon, domain: 'https://twitter.com/', url: tenantDetails?.twitterUrl || undefined },
      {
        icon: YoutubeIcon,
        domain: 'https://www.youtube.com/',
        url: tenantDetails?.youtubeUrl || undefined,
      },
    ];
    return (
      <div className="flex justify-center items-center gap-2">
        {socialMediaLinks
          .filter((item) => item.url)
          .map((item, index) => (
            <a key={index} href={item.domain + item.url} target="_blank" rel="noopener noreferrer">
              <Icon component={item.icon} />
            </a>
          ))}
      </div>
    );
  };
  return (
    <CustomModal
      modalTitle={t('ordersPage.orderDetailsPage.sendTrackingLinkModalTitle')}
      modalDescription={t('ordersPage.orderDetailsPage.sendTrackingLinkModalDescription')}
      open={isOpen}
      prefixCls="custom-modal-with-full-height"
      onCancel={() => setIsOpen(false)}
      footer={Footer}
      destroyOnClose
      keyboard={false}
      maskClosable={false}
      className="min-h-[600px]"
      
    >
      <Form
        scrollToFirstError={{ behavior: 'smooth' }}
        name="tracking-link-form"
        layout="vertical"
        form={form}
        className="custom-form"
        preserve={false}
      >
        <div className="flex flex-col gap-2">
          <Form.Item
            validateFirst
            label={t('ordersPage.orderDetailsPage.emailAddress')}
            name="email"
            rules={[
              {
                required: true,
                message: t('ordersPage.orderDetailsPage.emailRequired'),
              },
              { type: 'email', message: t('ordersPage.orderDetailsPage.emailInvalid') },
            ]}
          >
            <Input
              placeholder={t('ordersPage.orderDetailsPage.enterEmailPlaceholder')}
              maxLength={80}
            />
          </Form.Item>
          <Form.Item label={t('ordersPage.orderDetailsPage.message')} name="message">
            <TextArea
              placeholder={t('ordersPage.orderDetailsPage.enterMessagePlaceholder')}
              maxLength={500}
              className="!min-h-[200px]"
            />
          </Form.Item>
        </div>
        <span className="block mt-4">
          <div className="bg-[#F5F6FF] h-fit w-full rounded-md">
            <div className="flex justify-center items-center py-6">{renderTenantLogo()}</div>
            <div className="flex flex-col justify-center items-center text-primary-900 font-normal text-base">
              <div className="bg-white p-6 rounded-lg w-[70%] border border-[#E3E5F6]">
                <p className="pb-4">{messageWatcher}</p>
                <p className="pb-4">{t('ordersPage.orderDetailsPage.trackOrderMessage')}</p>
                <div className="flex justify-center items-center pb-4">
                  <Button type="primary" disabled className="h-[44px] px-8 !bg-primary-600 !text-white">
                    {t('ordersPage.orderDetailsPage.trackOrderButton')}
                  </Button>
                </div>
                <p className="pb-4">{t('ordersPage.orderDetailsPage.thankYouMessage')}</p>
              </div>
              <div className="text-primary-200 text-xs py-6 flex flex-col justify-center items-center">
                <p>{tenantDetails?.address?.line1 || ''}</p>
                <p>{t('ordersPage.orderDetailsPage.unsubscribeText')}</p>
              </div>
              <div className="pb-4 flex justify-center items-center gap-2">
                {renderSocialMediaIcons()}
              </div>
            </div>
          </div>
        </span>
      </Form>
    </CustomModal>
  );
};

export default SendTrackingLinkModal;
