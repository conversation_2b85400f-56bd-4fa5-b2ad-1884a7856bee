import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FooterLanguageToggle } from './FooterLanguageToggle';
import './Footer.css';
import { tenantsHook } from '@/api/tenants/useTenants';
import { DEV_CONFIG } from '@/config/devConfig';

const Footer: React.FC = () => {
  const { t } = useLanguage();
  const { data: tenantDetails } = tenantsHook.useEntity('tenantDetails', { staleTime: 60000 });

  return (
    <footer className="footer-container print:!hidden fixed bottom-0">
      <div className="footer-content">
        <div className="footer-copyright">
          Copyright ©{new Date().getFullYear()} {tenantDetails?.name || DEV_CONFIG.APP_NAME}. All
          rights reserved.
        </div>
        <div className="footer-links">
          <span className="footer-link">{t('auth.footer.terms')}</span>
          <span className="footer-link">{t('auth.footer.privacy')}</span>
          <span className="footer-link">{t('auth.footer.docs')}</span>
          <span className="footer-link">{t('auth.footer.helps')}</span>
        </div>
        <div className="footer-language">
          <FooterLanguageToggle />
        </div>
      </div>
    </footer>
  );
};

export default Footer;
