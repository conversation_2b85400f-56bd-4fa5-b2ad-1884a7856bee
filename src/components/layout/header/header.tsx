import { logout, useGetCurrentUser } from '@/api/auth/auth.service';
import { tenantsHook } from '@/api/tenants/useTenants';
import { tenantSettingsHook } from '@/api/tenantSettings/useTenantSettings';
import {
  OrderEntrySelectedIcon,
  OrderEntryIcon,
  OrderListIcon,
  AddressesIcon,
  ContactsIcon,
  LumigoLogo,
  LogoutIcon,
} from '@/assets';
import { ContactFilledIcon } from '@/assets/icons/contactFilledIcon';
import { InvoiceFilledIcon } from '@/assets/icons/invoiceFilledIcon';
import { InvoicesIcon } from '@/assets/icons/invoicesIcon';
import { LocationFilledIcon } from '@/assets/icons/locationFilledIcon';
import { OrderListsFilledIcon } from '@/assets/icons/orderListsFilledIcon';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { DEV_CONFIG } from '@/config/devConfig';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { SupportedLanguage } from '@/i18n/languageLoader';
import { removeStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';
import { UserOutlined, LockOutlined, LogoutOutlined, LoadingOutlined } from '@ant-design/icons';
import { Dropdown, MenuProps, Spin } from 'antd';
import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';

interface NavLink {
  title: string;
  path: string;
  active: boolean;
  icon: React.ReactNode;
}

interface HeaderProps {
  showLogoOnly?: boolean;
}

const Header = (props: HeaderProps) => {
  const handleMenuClick = (info: { key: string }) => {
    if (info.key === 'logout') {
      logoutHandler({});
    }
  };

  const { t, setLanguage } = useLanguage();
  const { navigate } = useNavigationContext();

  // Check if user is already on the target path
  const isOnPath = (targetPath: string): boolean => {
    // For order entry, check both order entry and order edit paths
    if (targetPath === ROUTES.ORDER.ORDER_ENTRY) {
      return currentPath === ROUTES.ORDER.ORDER_ENTRY || currentPath.startsWith('/orderEntry/');
    }

    // For order listing, only prevent if already on the exact listing page (not detail pages)
    if (targetPath === ROUTES.ORDER.LISTING) {
      return currentPath === ROUTES.ORDER.LISTING;
    }

    // For addresses, only prevent if already on the exact listing page
    if (targetPath === ROUTES.ADDRESSES.LISTING || targetPath === '/addresses') {
      return currentPath === ROUTES.ADDRESSES.LISTING || currentPath === '/addresses';
    }

    // For invoices, only prevent if already on the exact listing page
    if (targetPath === '/invoices' || targetPath === ROUTES.INVOICES.LISTING) {
      return currentPath === '/invoices' || currentPath === ROUTES.INVOICES.LISTING;
    }

    // For contacts, only prevent if already on the exact listing page
    if (targetPath === '/contact' || targetPath === ROUTES.CONTACT.LISTING) {
      return currentPath === '/contact' || currentPath === ROUTES.CONTACT.LISTING;
    }

    // Default: exact match
    return currentPath === targetPath;
  };

  const handleNavigation = (path: string) => {
    // Don't navigate if already on the target path
    if (isOnPath(path)) {
      setMobileMenuOpen(false);
      return;
    }

    navigate(path);
    setMobileMenuOpen(false);
  };

  const getAvatarText = (contactName?: string): string => {
    if (!contactName) return 'NA';

    const words = contactName.split(' ').filter(Boolean);

    if (words.length === 1) return words[0].charAt(0).toUpperCase();

    return `${words[0].charAt(0).toUpperCase()}${words[1].charAt(0).toUpperCase()}`;
  };

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const { data: userInfo } = useGetCurrentUser({ enabled: !props.showLogoOnly });
  const currentPath = window.location.pathname;
  const {
    data: tenantDetails,
    isFetching,
    isLoading,
  } = tenantsHook.useEntity('tenantDetails', { staleTime: 60000 });

  const { data: tenantSettings } = tenantSettingsHook.useEntity('GlobalConfiguration', {
    staleTime: 60000,
  });

  useLayoutEffect(() => {
    if (!tenantSettings) return;

    const apiLang = tenantSettings.globalConfiguration?.language;
    const mappedLang: SupportedLanguage = apiLang === 'en' ? 'EN' : 'FR';

    const storedLang = localStorage.getItem('language');

    if (storedLang !== mappedLang) {
      localStorage.setItem('language', mappedLang);
    }

    setLanguage(mappedLang);
  }, [tenantSettings, setLanguage]);

  const profileMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: t('header.profile.profile'),
      icon: <UserOutlined />,
    },
    {
      key: 'reset-password',
      label: t('header.profile.resetPassword'),
      icon: <LockOutlined />,
    },
    {
      key: 'logout',
      label: t('header.profile.logout'),
      icon: <LogoutOutlined />,
      onClick: () => logoutHandler({}),
    },
  ];

  const navLinks: NavLink[] = useMemo(() => {
    const links = [
      {
        title: t('header.navigation.orderEntry'),
        path: ROUTES.ORDER.ORDER_ENTRY,
        active: currentPath.includes(ROUTES.ORDER.ORDER_ENTRY || ROUTES.ORDER.ORDER_EDIT),
        icon: (
          <span className="mr-1 pt-[1px]">
            {currentPath === ROUTES.ORDER.ORDER_ENTRY || currentPath === ROUTES.ORDER.ORDER_EDIT ? (
              <OrderEntrySelectedIcon />
            ) : (
              <OrderEntryIcon bool={false} />
            )}
          </span>
        ),
      },
      {
        title: t('header.navigation.orderList'),
        path: ROUTES.ORDER.LISTING,
        active: currentPath.includes(ROUTES.ORDER.LISTING),
        icon: (
          <span className="mr-1 pt-[1px]">
            {currentPath === ROUTES.ORDER.LISTING || currentPath.includes('/orders/') ? (
              <OrderListsFilledIcon bool={true} />
            ) : (
              <OrderListIcon bool={currentPath.includes(ROUTES.ORDER.LISTING)} />
            )}
          </span>
        ),
      },
    ];

    if (userInfo?.permissions?.address) {
      links.push({
        title: t('header.navigation.addresses'),
        path: ROUTES.ADDRESSES.LISTING || '/addresses',
        active:
          currentPath.includes(ROUTES.ADDRESSES.LISTING) || currentPath.includes('/addresses/'),
        icon: (
          <span className="mr-1 pt-[1px]">
            {currentPath === ROUTES.ADDRESSES.LISTING ? (
              <LocationFilledIcon bool={true} />
            ) : (
              <AddressesIcon bool={currentPath.includes(ROUTES.ADDRESSES.LISTING)} />
            )}
          </span>
        ),
      });
    }

    if (userInfo?.permissions?.invoices) {
      links.push({
        title: t('header.navigation.invoices'),
        path: '/invoices',
        active: currentPath.includes('/invoices'),
        icon: (
          <span className="mr-1 pt-[1px]">
            {currentPath === ROUTES.INVOICES.LISTING ? (
              <InvoiceFilledIcon bool={true} />
            ) : (
              <InvoicesIcon bool={currentPath.includes(ROUTES.INVOICES.LISTING)} />
            )}
          </span>
        ),
      });
    }

    if (userInfo?.isPrimary) {
      links.push({
        title: t('header.navigation.contact'),
        path: '/contact',
        active: currentPath.includes('/contact'),
        icon: (
          <span className="mr-1 pt-[1px]">
            {}
            {currentPath === ROUTES.CONTACT.LISTING ? (
              <ContactFilledIcon bool={true} />
            ) : (
              <ContactsIcon bool={currentPath.includes(ROUTES.CONTACT.LISTING)} />
            )}{' '}
          </span>
        ),
      });
    }

    return links;
  }, [currentPath, userInfo?.permissions, userInfo?.isPrimary, t]);

  const logoutHandler = (
    e: React.MouseEvent | { preventDefault?: () => void; stopPropagation?: () => void }
  ) => {
    if (e.preventDefault && e.stopPropagation) {
      e.preventDefault();
      e.stopPropagation();
    }
    setMobileMenuOpen(false);

    customAlert.warning({
      title: t('auth.logoutConfirmation'),
      message: t('auth.redirectTxt'),
      firstButtonFunction: async () => {
        try {
          console.log('Attempting to logout');
          await logout();
          customAlert.destroy();
          window.location.href = ROUTES.COMMON.LOGIN;
        } catch (error) {
          console.error('Logout failed:', error);
          removeStorageItem(StorageKeys.USER_INFO);
          removeStorageItem(StorageKeys.IS_AUTHENTICATED);
          window.location.href = ROUTES.COMMON.LOGIN;
        }
      },
      secondButtonFunction: () => customAlert.destroy(),
      firstButtonTitle: t('common.logout'),
      secondButtonTitle: t('common.cancel'),
    });
  };

  const renderTenantLogo = () => {
    if (isFetching || isLoading) {
      return (
        <div className="flex justify-center items-center w-3/4 h-20">
          <Spin indicator={<LoadingOutlined spin className="text-primary-600 block" />} />
        </div>
      );
    }
    const tenantLogo = tenantDetails?.brandLogo ? tenantDetails?.brandLogo : LumigoLogo;
    const tenantName = tenantDetails?.name ? tenantDetails?.name : DEV_CONFIG.APP_NAME;

    return (
      <img
        src={tenantLogo}
        alt={tenantName}
        className={`${tenantDetails?.brandLogo ? 'h-[50px]' : 'h-[40px]'} cursor-pointer `}
        onClick={() => {
          if (!props.showLogoOnly && !isOnPath(ROUTES.ORDER.ORDER_ENTRY)) {
            navigate(ROUTES.ORDER.ORDER_ENTRY);
          }
        }}
      />
    );
  };

  useEffect(() => {
    if (tenantDetails?.name) {
      document.title = tenantDetails.name;

      // change favicon
      const link: HTMLLinkElement =
        document.querySelector("link[rel='icon']") || document.createElement('link');
      link.rel = 'icon';
      link.type = 'image/png';
      link.href = tenantDetails?.favicon || '/default-favicon.png'; // fallback icon
      document.head.appendChild(link);
    }
  }, [tenantDetails?.name, tenantDetails?.favicon]);

  return (
    <header className="app-header">
      <div className="header-container">
        <div className={`header-logo ${props.showLogoOnly && '!w-full'}`}>
          <div className={`logo-wrapper ${props.showLogoOnly && 'logo-wrapper-without-content'}`}>
            {renderTenantLogo()}
          </div>
        </div>
        {!props.showLogoOnly && (
          <>
            <div className="md:hidden print:!hidden">
              <button
                type="button"
                className="mobile-menu-button mobile-menu-toggle"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>

            <div className="hidden md:flex flex-1 justify-center">
              <nav className="flex">
                {navLinks.map((link, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleNavigation(link.path)}
                    className={`flex items-center px-4 py-4 font-medium cursor-pointer border-0 bg-transparent ${
                      link.active
                        ? 'text-primary-600 border-b-2 border-primary-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {link.icon}
                    {link.title}
                  </button>
                ))}
              </nav>
            </div>

            <div className="hidden print:!hidden md:flex items-center ml-4 relative">
              <Dropdown
                menu={{
                  items: profileMenuItems,
                  onClick: handleMenuClick,
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="flex items-center cursor-pointer profile-toggle">
                  <div className="h-8 w-8 flex items-center justify-center bg-indigo-50 text-indigo-700 text-sm font-medium">
                    {getAvatarText(userInfo?.name)}
                  </div>
                  <span className="px-2 text-gray-700 font-medium hidden sm:inline">
                    {userInfo?.name}
                  </span>
                  <svg
                    className="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </Dropdown>
            </div>

            <div className="md:hidden flex items-center print:!hidden">
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'logout',
                      label: t('header.profile.logout'),
                      icon: (
                        <img
                          src={LogoutIcon}
                          alt={t('header.profile.logout')}
                          className="w-5 h-5 mr-1"
                        />
                      ),
                      onClick: () => logoutHandler({}),
                    },
                  ],
                  onClick: handleMenuClick,
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="h-8 w-8 flex items-center justify-center bg-indigo-50 text-indigo-700 text-sm font-medium rounded-full profile-toggle">
                  {getAvatarText(userInfo?.name)}
                </div>
              </Dropdown>
            </div>
          </>
        )}
      </div>

      {!props.showLogoOnly && mobileMenuOpen && (
        <div className="md:hidden" ref={mobileMenuRef}>
          <div className="mobile-menu">
            {navLinks.map((link, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleNavigation(link.path)}
                className={`mobile-menu-item border-0 bg-transparent ${
                  link.active ? 'active-mobile-link' : 'inactive-mobile-link'
                }`}
              >
                <span className="w-[15px] h-[15px]">{link.icon}</span>
                {link.title}
              </button>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
