import { PaginationParams, FilterParams } from '@/api/core/types';
import { IColDef, GridStorageKey } from '@/types/AgGridTypes';
import { AppEvents } from '@/types/AppEvents';

export interface OptionsInterface {
  value: string;
  label: string;
  type: string;
  dropDownOptions?: any[];
}

export interface AdvanceFilterColDefs extends IColDef {
  dropDownOptions?: any[];
}
export interface SearchedProps {
  searchedValues?: (e: string) => void;
  colDefs: AdvanceFilterColDefs[];
  searchInputPlaceholder?: string;
  className?: string;
  advanceFilter?: boolean;
  clearAllFunctionRef?: React.MutableRefObject<{
    handleClearAll: () => void;
  }>;
  onSearch?: (value: string) => void;
  supportedFields?: string[];
  searchText?: string;
  setSearchText?: React.Dispatch<React.SetStateAction<string>>;
  gridName?: GridStorageKey;
}
interface IAssignedFilters {
  field: string;
  operator: string;
  value: string;
  label: string;
}
export interface IWithQuickFilter {
  isSetQuickFilter?: true;
  setQuickFilters: (filters: string, data: IAssignedFilters[]) => void;

  /**
   * Unique event identifier used to trigger quick filter events
   */
  quickFilterEventKey: keyof AppEvents;
  /**
   * Unique event identifier used to trigger quick filter title events
   */
  quickFilterTitleEventKey: keyof AppEvents;
  /**
   * Key used to store quick filter settings in the backend
   */
  quickFilterSettingsKey: GridStorageKey;
}

export interface IWithoutQuickFilter {
  isSetQuickFilter?: false;
}

export interface IWithoutAdvanceFilter {
  advanceFilter?: false;
}

export interface IWithAdvanceFilter {
  setSelectedQuickFilterData: React.Dispatch<React.SetStateAction<IAssignedFilters[]>>;
  setFilterParams: (value: React.SetStateAction<PaginationParams & FilterParams>) => void;
  onFilterApply: (data: { filters: IAssignedFilters[] }) => void;
}

export interface IAdvanceFilterObj {
  where: Record<string, Record<any, string>>;
}
export type ISearchedProps = SearchedProps &
  (IWithQuickFilter | IWithoutQuickFilter) &
  (IWithAdvanceFilter | IWithoutAdvanceFilter);
