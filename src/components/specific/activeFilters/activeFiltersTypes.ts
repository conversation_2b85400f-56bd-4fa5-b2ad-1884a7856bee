import { IColDef } from '@/types/AgGridTypes';

export interface IAssignedFilters {
  field: string;
  operator: string;
  value: string | any;
  label: string;
  type?: string;
  //used for number between operator
  from?: string;
  to?: string;
}

export interface IActiveFiltersProps {
  selectedQuickFilterData: IAssignedFilters[];
  clearAllToDefault: () => void;
  colDefs: IColDef[];
  className?: string;
}
