import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { ITenantSettings, ITenantSettingsDto } from './tenantSettings.types';

export class TenantSettingsService extends BaseService<ITenantSettings, ITenantSettingsDto, ITenantSettingsDto> {
    /**
     * Creates a new instance of ZoneService
     * @param axios - Configured Axios instance
     */
    constructor(axios: AxiosInstance) {
        super(axios, '/api/v1/tenant-settings/customers');
    }
}
