export interface INotificationPreference {
  order: {
    orderEdit: {
      admin: boolean;
      sender: boolean;
      partner: boolean;
      recipient: boolean;
    };
    orderPickup: {
      admin: boolean;
      sender: boolean;
      partner: boolean;
      recipient: boolean;
    };
    orderPlaced: {
      admin: boolean;
      sender: boolean;
      partner: boolean;
      recipient: boolean;
    };
    orderCancelled: {
      admin: boolean;
      sender: boolean;
      partner: boolean;
      recipient: boolean;
    };
    orderCompleted: {
      admin: boolean;
      sender: boolean;
      partner: boolean;
      recipient: boolean;
    };
  };
}

export interface IUIConfigurationItem {
  fieldIdentifier: string;
  label: string;
  isRequired: boolean;
  isSystemRequired: boolean;
  isVisible: boolean;
  placeholder?: string;
  tooltip?: string;
  hasChildren: boolean;
  children: IUIConfigurationItem[];
}

export interface IOrderManagementSettings {
  trackingLinkExpiry: number;
  orderEditTimeLimit: number;
  showPriceIncludingTaxes: boolean;
  allowCancellationDuringTransit: boolean;
  allowCancellationWhenSubmitted: boolean;
  submittedCancellationMessage: string;
  transitOrderCancellationMessage: string;
}

export enum BillingFrequency {
  AUTOMATED = 'automated',
  MANUAL = 'manual',
}

export enum BillingCycle {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export enum WeeklyBillingDay {
  MONDAY = 'Monday',
  TUESDAY = 'Tuesday',
  WEDNESDAY = 'Wednesday',
  THURSDAY = 'Thursday',
  FRIDAY = 'Friday',
  SATURDAY = 'Saturday',
  SUNDAY = 'Sunday',
}

export enum MonthlyBillingDay {
  FIRST = '1th',
  FIFTH = '5th',
  TENTH = '10th',
  FIFTEENTH = '15th',
  TWENTIETH = '20th',
}

export interface IBillingSetting {
  billingFrequency: string;
  billingCycle: BillingCycle;
  billingDay: string;
  currency: string;
  province: string;
  gstNumber: string;
  pstNumber: string;
  gstTax: number;
  pstTax: number;
  stripeTestKey: string;
  stripeLiveKey: string;
}

export enum Languages {
  EN = 'en',
  FR = 'fr',
}

export enum TimeFormats {
  TWELVE_HOUR = '12hr',
  TWENTY_FOUR_HOUR = '24hr',
}

export enum DistanceMeasures {
  KILOMETERS = 'km',
  MILES = 'miles',
}

export enum WeightMeasures {
  POUNDS = 'lb',
  KILOGRAMS = 'kg',
}

export enum LengthMeasures {
  CENTIMETERS = 'cm',
  INCHES = 'inch',
}

export interface IGlobalConfiguration {
  timeZone: string;
  language: Languages;
  timeFormat: TimeFormats;
  distanceUnit: DistanceMeasures;
  weightUnit: WeightMeasures;
  dimensionUnit: LengthMeasures;
}

export interface IUIConfiguration {
  UIConfigurationSettings: IUIConfigurationItem[];
}

export interface ITenantSettingsDto {
  notificationPreference?: INotificationPreference;
  globalConfiguration?: IGlobalConfiguration;
  billingSetting?: IBillingSetting;
  orderManagementSetting?: IOrderManagementSettings;
  uiConfiguration?: IUIConfiguration;
}

export interface ITenantSettings extends ITenantSettingsDto {
  id: string;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
