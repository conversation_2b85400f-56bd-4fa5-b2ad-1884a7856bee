import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { TenantSettingsService } from './tenantSettings.service';
import { ITenantSettings, ITenantSettingsDto } from './tenantSettings.types';

export const tenantSettingsService = new TenantSettingsService(apiClient.getAxiosInstance());

export const tenantSettingsHook = createEntityHooks<ITenantSettings, ITenantSettingsDto, ITenantSettingsDto>(
    QueryHookKey.tenantSettings,
    tenantSettingsService
);
