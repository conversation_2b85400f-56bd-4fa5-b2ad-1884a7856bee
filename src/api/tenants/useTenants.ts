import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { TenantsService } from './tenants.service';
import { ITenants, ITenantsDto } from './tenants.types';

export const tenantsService = new TenantsService(apiClient.getAxiosInstance());

export const tenantsHook = createEntityHooks<ITenants, ITenantsDto, ITenantsDto>(
  QueryHookKey.tenants,
  tenantsService
);
