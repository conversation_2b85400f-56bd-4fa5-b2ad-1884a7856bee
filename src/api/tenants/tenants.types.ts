export interface ITenantAddress {
  [key: string]: any;
}

export interface ITenantsDto {
  name: string;
  companyShortName: string;
  companyTagLine: string;
  companyWebsite: string;
  companyUniqueId: string;
  description: string;
  status: boolean;
  contactEmail: string;
  contactPhone: string;
  faxNumber: string;
  address: ITenantAddress;
  brandLogo: string;
  favicon: string;
  faceBookUrl: string;
  twitterUrl: string;
  linkedInUrl: string;
  instagramUrl: string;
  whatsAppUrl: string;
  youtubeUrl: string;
}

export interface ITenants extends ITenantsDto {
  id: string;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
