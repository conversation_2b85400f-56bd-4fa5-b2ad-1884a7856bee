import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { ITenants, ITenantsDto } from './tenants.types';

export class TenantsService extends BaseService<ITenants, ITenantsDto, ITenantsDto> {
  /**
   * Creates a new instance of TenantsService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/tenants/customers');
  }
}
