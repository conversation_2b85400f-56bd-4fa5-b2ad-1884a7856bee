type EntityType = 'order';
type FileType = 'attachment';

export interface IUploadFileDto {
  file: File;
  entityType: string;
  entityId: string | null;
  type: string;
  userId?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface GetUploadedFileDto {
  id: string;
  fileId?: string;
  filename: string;
  originalFilename: string;
  type: FileType;
  mimeType: string;
  size: string;
  path: string;
  url: string;
  entityType: EntityType;
  entityId: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string | null;
  status: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface ICreateFileUploadResponse {
  success: boolean;
  fileId: string;
  filename: string;
  originalName: string;
  url: string;
}
