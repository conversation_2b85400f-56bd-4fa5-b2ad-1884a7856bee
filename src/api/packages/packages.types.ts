export interface IPackage {
  id: string;
  name: string;
  description?: string;
  status: string;
  capabilities: string[];
  dimensionsRequired: boolean;
  declaredValue?: number;
  weightRequired: boolean;
  requiresSignature: boolean;
  requiresInsurance: boolean;
  specialHandlingInstructions?: string;
  metadata: {
    isPrimary: boolean;
  };
  height: number;
  width: number;
  length: number;
  quantity: number;
  cubicDimention: string;
  totalWeight: number;
}

export interface IPackagesList {
  packages: IPackage[];
}
