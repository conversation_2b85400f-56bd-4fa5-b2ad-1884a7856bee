import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { CustomerSettingsService } from './customerSettings.service';
import { ICustomerSettings, ICustomerSettingsDto } from './customerSettings.types';

export const customerSettingsService = new CustomerSettingsService(apiClient.getAxiosInstance());

export const customerSettingsHook = createEntityHooks<ICustomerSettings, ICustomerSettingsDto, ICustomerSettingsDto>(
    QueryHookKey.Customer_Settings,
    customerSettingsService
);
