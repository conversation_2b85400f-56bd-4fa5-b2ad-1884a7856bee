import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { ICustomerSettings, ICustomerSettingsDto } from './customerSettings.types';

export class CustomerSettingsService extends BaseService<ICustomerSettings, ICustomerSettingsDto, ICustomerSettingsDto> {
    /**
     * Creates a new instance of CustomerSettingsService
     * @param axios - Configured Axios instance
     */
    constructor(axios: AxiosInstance) {
        super(axios, '/api/v1/customer-portal/settings');
    }
}
