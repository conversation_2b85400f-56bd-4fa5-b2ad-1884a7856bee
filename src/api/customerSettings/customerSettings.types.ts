export interface INotificationOrderSettings {
  orderEdit: { admin: boolean; sender: boolean; partner: boolean; recipient: boolean; };
  orderPickup: { admin: boolean; sender: boolean; partner: boolean; recipient: boolean; };
  orderPlaced: { admin: boolean; sender: boolean; partner: boolean; recipient: boolean; };
  orderCancelled: { admin: boolean; sender: boolean; partner: boolean; recipient: boolean; };
  orderCompleted: { admin: boolean; sender: boolean; partner: boolean; recipient: boolean; };
}

export interface INotificationPreference {
  sms: {
    order: INotificationOrderSettings;
  };
  email: {
    order: INotificationOrderSettings;
  };
}

export interface IUIConfigurationItem {
  fieldIdentifier: string;
  label: string;
  isRequired: boolean;
  isSystemRequired: boolean;
  isVisible: boolean;
  placeholder?: string;
  tooltip?: string;
  hasChildren: boolean;
  children: IUIConfigurationItem[];
}

export interface IGeneralSettings {
  [key: string]: any;
}
export interface IUIConfigurationDto {
  value: IUIConfigurationItem[];
}
export interface ICustomerSettingsDto {
  uiConfiguration?: IUIConfigurationDto;
  UIConfigurationSettings?: IUIConfigurationItem[];
  notificationPreference?: INotificationPreference;
  general?: IGeneralSettings;
  value?: {
    sms?: {
      order: INotificationOrderSettings;
    };
    email?: {
      order: INotificationOrderSettings;
    };
  } | IUIConfigurationItem[];
  sms?: {
    order: INotificationOrderSettings;
  };
  email?: {
    order: INotificationOrderSettings;
  };
}

export interface ICustomerSettings extends ICustomerSettingsDto {
  id: string;
  customerId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
