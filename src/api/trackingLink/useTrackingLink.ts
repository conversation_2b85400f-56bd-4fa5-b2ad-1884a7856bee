import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { apiClient } from '..';
import { TrackingLinkService } from './trackingLink.service';
import { ITrackingLink, ITrackingLinkResponse } from './trackingLink.types';

export const trackingLinkService = new TrackingLinkService(apiClient.getAxiosInstance());

export const TrackingLinkServiceHook = createEntityHooks<
  ITrackingLinkResponse,
  ITrackingLink,
  ITrackingLink
>('trackingLinkService', trackingLinkService);
