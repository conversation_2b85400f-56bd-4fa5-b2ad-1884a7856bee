import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { ITrackingLinkResponse, ITrackingLink } from './trackingLink.types';

export class TrackingLinkService extends BaseService<
  ITrackingLinkResponse,
  ITrackingLink,
  ITrackingLink
> {
  /**
   * Creates a new instance of Address
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/customer-portal/order-tracking');
  }
}
