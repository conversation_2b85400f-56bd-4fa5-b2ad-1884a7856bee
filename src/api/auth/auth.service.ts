import { removeStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';
import { apiClient } from '..';
import { AUTH_ENDPOINTS } from '../endpoints/AuthEndpoints';
import { ROUTES } from '@/constant/RoutesConstant';
import { ICreateVerification, ILogin, IUser, IVerifyOTP, IResetPassword } from './auth.types';
import { QueryKey, useQuery, UseQueryOptions } from '@tanstack/react-query';

const api = apiClient.getAxiosInstance();

export const login = async (payload: ILogin): Promise<{ user: IUser } | void> => {
  const response = await api.post(AUTH_ENDPOINTS.LOGIN, payload);

  if (response.status === 200 && response.data) {
    return response.data as { user: IUser };
  }
};

export const logout = async (): Promise<void> => {
  const response = await api.post(AUTH_ENDPOINTS.LOGOUT);

  if (response.status === 200) {
    removeStorageItem(StorageKeys.USER_INFO);
    removeStorageItem(StorageKeys.IS_AUTHENTICATED);
    window.location.replace(ROUTES.COMMON.LOGIN);
  }
};

export const getUserInfo = async (): Promise<IUser | null> => {
  const response = await api.get(AUTH_ENDPOINTS.ME);

  if (response.status === 200 && response.data) {
    return response.data.contact;
  }
  return null;
};

export const useGetCurrentUser = (
  options?: Partial<UseQueryOptions<unknown, Error, IUser, QueryKey>> | undefined
) => {
  return useQuery({
    ...options,
    queryKey: ['contactDetails'],
    queryFn: async () => await getUserInfo(),
    staleTime: 120000,
    enabled: options?.enabled,
    retry: 0,
  });
};

export const createVerification = async (
  payload: ICreateVerification
): Promise<{ verificationId: string } | void> => {
  const response = await api.post(AUTH_ENDPOINTS.CREATE_VERIFICATION, payload);

  if (response.status === 201 && response.data) {
    return { verificationId: response.data?.verificationId };
  }
};

export const verifyOTP = async (payload: IVerifyOTP): Promise<boolean> => {
  const response = await api.put(AUTH_ENDPOINTS.VERIFY_OTP(payload.verificationId), {
    otp: Number(payload.otp),
  });

  if (response.status === 204) {
    return true;
  }
  return false;
};

export const resetPassword = async (payload: IResetPassword): Promise<boolean> => {
  const response = await api.put(AUTH_ENDPOINTS.RESET_PASSWORD(payload.verificationId), {
    password: payload.password,
    confirmPassword: payload.confirmPassword,
  });

  if (response.status === 204) {
    return true;
  }
  return false;
};
